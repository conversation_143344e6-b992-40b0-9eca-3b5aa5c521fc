# 统一价格配置系统

## 概述

为了解决价格配置散落在各个文件中的问题，我们创建了一个统一的价格配置系统。所有与价格相关的配置现在都集中在 `/src/constants/pricing.ts` 文件中。

## 配置文件结构

### 基础价格配置 (PRICING_CONFIG)

```typescript
export const PRICING_CONFIG = {
  PRIORITY_LAUNCH: {
    displayPrice: 99,        // 显示价格（元）
    stripeAmount: 9900,      // Stripe价格（分为单位）
    currency: 'CNY',         // 货币
    stripeCurrency: 'usd',   // Stripe货币代码
    productName: 'AI工具优先发布服务',
    description: '让您的AI工具获得优先审核和推荐位置',
    features: [...]          // 功能特性列表
  },
  FREE_LAUNCH: {
    displayPrice: 0,
    stripeAmount: 0,
    currency: 'CNY',
    stripeCurrency: 'cny',
    productName: '免费发布服务',
    description: '选择一个月后的任意发布日期',
    features: [...]
  }
}
```

### 发布选项配置 (LAUNCH_OPTIONS)

基于基础价格配置生成的发布选项数组，用于LaunchDateSelector组件。

### 工具定价类型配置 (TOOL_PRICING_TYPES)

定义工具的定价类型（免费、免费增值、付费）及其显示样式。

### 选项数组

- `TOOL_PRICING_OPTIONS`: 用于筛选的价格选项（包含"所有价格"选项）
- `TOOL_PRICING_FORM_OPTIONS`: 用于表单的价格选项（不包含"所有价格"选项）

## 辅助函数

### 价格格式化
- `formatPrice(price: number)`: 格式化显示价格（0 → "免费", 99 → "¥99"）
- `formatStripeAmount(amount: number, currency?: string)`: 格式化Stripe金额显示

### 工具定价相关
- `getToolPricingColor(pricing: string)`: 获取定价类型的颜色样式
- `getToolPricingText(pricing: string)`: 获取定价类型的显示文本
- `getPricingConfig(optionId: LaunchOptionId)`: 根据选项ID获取对应的价格配置

## 已更新的文件

### 核心配置文件
- `/src/constants/pricing.ts` - 新建的统一配置文件

### Stripe相关
- `/src/lib/stripe.ts` - 使用统一配置替换硬编码价格
- `/src/components/StripeCheckoutForm.tsx` - 使用formatStripeAmount函数
- `/src/app/api/stripe/create-payment-intent/route.ts` - 移除未使用的导入

### 组件和页面
- `/src/components/LaunchDateSelector.tsx` - 使用LAUNCH_OPTIONS和formatPrice
- `/src/components/ToolCard.tsx` - 使用统一的定价样式函数
- `/src/app/tools/[id]/page.tsx` - 使用统一的定价样式函数
- `/src/app/tools/page.tsx` - 使用TOOL_PRICING_OPTIONS
- `/src/app/submit/page.tsx` - 使用TOOL_PRICING_FORM_OPTIONS
- `/src/app/submit/edit/[toolId]/page.tsx` - 使用TOOL_PRICING_FORM_OPTIONS
- `/src/app/payment/checkout/page.tsx` - 使用formatStripeAmount函数
- `/src/app/test-stripe/page.tsx` - 使用统一配置

### API路由
- `/src/app/api/tools/[id]/launch-date/route.ts` - 使用PRICING_CONFIG.PRIORITY_LAUNCH.stripeAmount

## 优势

1. **单一数据源**: 所有价格相关配置都在一个文件中，便于维护
2. **类型安全**: 使用TypeScript的`as const`断言确保类型安全
3. **一致性**: 所有组件使用相同的价格格式化函数
4. **易于修改**: 修改价格只需要在一个地方进行
5. **避免遗漏**: 不会因为忘记更新某个文件而导致价格不一致

## 使用示例

```typescript
import { 
  PRICING_CONFIG, 
  formatPrice, 
  formatStripeAmount,
  getToolPricingColor 
} from '@/constants/pricing';

// 显示价格
const displayPrice = formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice); // "¥99"

// Stripe金额格式化
const stripePrice = formatStripeAmount(9900); // "¥99.00"

// 工具定价样式
const pricingColor = getToolPricingColor('free'); // "bg-green-100 text-green-800"
```

## 测试

创建了测试页面 `/test-pricing` 来验证所有配置和函数是否正常工作。

## 注意事项

1. 修改价格时，只需要更新 `/src/constants/pricing.ts` 文件
2. 添加新的价格类型时，需要同时更新相关的类型定义和辅助函数
3. 确保Stripe的价格单位是分（cents），显示价格单位是元（yuan）
4. 所有新的价格相关功能都应该使用这个统一配置系统
