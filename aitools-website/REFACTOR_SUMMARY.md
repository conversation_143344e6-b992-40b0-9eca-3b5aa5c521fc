# 工具状态系统重构总结

## 概述

本次重构移除了工具状态中的 'published' 状态，改为使用 'approved' 状态结合 `launchDate` 字段来判断工具是否已发布。同时将 `publishedAt` 字段替换为 `launchDate` 字段。

## 主要变更

### 1. 数据模型变更

**文件**: `src/models/Tool.ts`
- 移除状态枚举中的 'published'，保留: 'draft', 'pending', 'approved', 'rejected'
- 将 `publishedAt?: Date` 字段替换为 `launchDate?: Date`
- 更新相关索引

**文件**: `src/lib/api.ts`
- 更新 Tool 接口，将 `publishedAt?: string` 替换为 `launchDate?: string`
- 移除状态类型中的 'published'

### 2. 发布逻辑变更

**新的发布判断逻辑**:
```typescript
const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date();
```

**替换所有的**:
```typescript
// 旧逻辑
tool.status === 'published'

// 新逻辑  
tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date()
```

### 3. API 路由更新

**文件**: `src/app/api/tools/route.ts`
- 更新查询逻辑，将 `status: 'published'` 映射为 `status: 'approved' && launchDate <= now`
- 更新日期过滤使用 `launchDate` 而非 `publishedAt`

**文件**: `src/app/api/tools/publish/route.ts`
- 修改 cron job 逻辑，设置 `launchDate` 而不是改变状态为 'published'
- 保持工具状态为 'approved'

**文件**: `src/app/api/tools/[id]/launch-date/route.ts`
- 确保设置发布日期时同时更新 `selectedLaunchDate` 和 `launchDate`

**文件**: `src/app/api/categories/route.ts`
- 更新分类统计查询，使用 `status: 'approved' && launchDate <= now`

### 4. 前端组件更新

**服务端渲染页面**:
- `src/app/tools/[id]/page.tsx`: 更新工具详情页发布状态检查
- `src/app/page.tsx`: 更新首页数据获取API调用

**管理员界面**:
- `src/app/admin/tools/[id]/page.tsx`: 更新状态显示逻辑，添加发布状态判断
- `src/app/admin/page.tsx`: 更新工具列表显示
- `src/app/dashboard/page.tsx`: 更新用户仪表板

**组件**:
- `src/components/tools/ToolDetailClient.tsx`: 更新发布日期显示
- 各种工具卡片组件: 更新日期字段引用

### 5. SEO 和元数据

**文件**: `src/app/sitemap.xml/route.ts`
- 更新 sitemap 生成，使用新的发布逻辑和 `launchDate`

**文件**: `src/lib/seo/structuredData.ts`
- 更新结构化数据，使用 `launchDate` 替代 `publishedAt`

## 数据迁移

虽然本次重构主要是代码层面的更改，但如果数据库中存在使用旧字段的数据，可能需要进行数据迁移：

```javascript
// 示例迁移脚本（需要根据实际情况调整）
db.tools.updateMany(
  { status: 'published' },
  { 
    $set: { 
      status: 'approved',
      launchDate: '$publishedAt'
    },
    $unset: { publishedAt: 1 }
  }
);
```

## 测试验证

创建了测试脚本 `test-status-refactor.js` 来验证重构的正确性：
- 测试已发布工具查询
- 测试待发布工具查询  
- 测试状态统计
- 测试发布逻辑

## 向后兼容性

- API 接口保持兼容，前端仍可使用 `status: 'published'` 参数
- 后端会自动将此参数映射为新的查询逻辑
- 现有的前端代码无需立即更改

## 注意事项

1. **Cron Job**: 确保定时发布任务正确配置，使用新的发布逻辑
2. **索引**: 可能需要为 `launchDate` 字段添加数据库索引以优化查询性能
3. **缓存**: 如果使用了缓存，需要清除相关缓存
4. **监控**: 监控发布流程确保工具能正确在指定时间发布

## 受影响的功能

- ✅ 工具提交和审核流程
- ✅ 工具发布和展示
- ✅ 管理员界面
- ✅ 用户仪表板
- ✅ SEO 和 sitemap
- ✅ API 接口
- ✅ 定时发布任务

## 完成状态

- [x] 数据模型更新
- [x] API 路由更新  
- [x] 前端组件更新
- [x] 管理员界面更新
- [x] SEO 相关更新
- [x] 测试脚本创建
- [ ] 数据库迁移（如需要）
- [ ] 生产环境部署验证
