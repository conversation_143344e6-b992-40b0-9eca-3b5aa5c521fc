{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware';\nimport { NextResponse } from 'next/server';\n\nexport default withAuth(\n  function middleware(req) {\n    // 如果用户已经通过了认证检查，这里可以添加额外的逻辑\n    return NextResponse.next();\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        // 检查是否访问管理页面\n        if (req.nextUrl.pathname.startsWith('/admin')) {\n          // 管理页面需要管理员权限\n          return token?.role === 'admin';\n        }\n        \n        // 其他受保护的页面只需要登录\n        return !!token;\n      },\n    },\n  }\n);\n\n// 配置需要保护的路径\nexport const config = {\n  matcher: [\n    '/admin/:path*',\n    '/profile/:path*',\n    '/submit/:path*',\n  ]\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;IACrB,4BAA4B;IAC5B,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,aAAa;YACb,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;gBAC7C,cAAc;gBACd,OAAO,OAAO,SAAS;YACzB;YAEA,gBAAgB;YAChB,OAAO,CAAC,CAAC;QACX;IACF;AACF;AAIK,MAAM,SAAS;IACpB,SAAS;QACP;QACA;QACA;KACD;AACH"}}]}