{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/profile/:path*{(\\\\.json)}?", "originalSource": "/profile/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/submit/:path*{(\\\\.json)}?", "originalSource": "/submit/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Zs+C5MwLg/q5riD+Y7a6/hdqafbZ6iS37AeAQ4wmY9k=", "__NEXT_PREVIEW_MODE_ID": "6e518af49dce921753559fccca61a432", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2741babfd73f166982d0ef170aae632c59d4b389e9260e9ea139e00e0c54404e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f7c1e6ead9939fd913297765198187dba416c7e6b97c85e337633b690046156e"}}}, "instrumentation": null, "functions": {}}