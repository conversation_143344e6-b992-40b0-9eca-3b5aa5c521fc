(()=>{var e={};e.id=8644,e.ids=[8644],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,s)=>{"use strict";s.d(t,{N:()=>u});var r=s(36344),i=s(65752),n=s(13581),a=s(75745),o=s(17063);let u={providers:[(0,r.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,n.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,a.A)();let t=await o.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let s=t.emailVerificationToken;if(!s||!s.includes(":"))return null;let[r,i]=s.split(":");if(r!==e.token||i!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:s}){if(t?.provider==="email-code")return!0;await (0,a.A)();try{let r=await o.A.findOne({email:e.email});return r?r.lastLoginAt=new Date:r=new o.A({email:e.email,name:e.name||s?.name||"User",avatar:e.image||s?.image,emailVerified:!0,lastLoginAt:new Date}),await r.save(),t&&"email-code"!==t.provider&&(r.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await r.save()),e.id=r._id.toString(),e.role=r.role,!0}catch(e){return console.error("Sign in error:",e),!1}},async jwt({token:e,user:t}){if(t)e.userId=t.id,e.role=t.role||"user";else if(e.userId)try{await (0,a.A)();let t=await o.A.findById(e.userId);t&&(e.role=t.role)}catch(e){console.error("Error fetching user role:",e)}return e},session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(56037),i=s.n(r);let n=new r.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),a=new r.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[n],submittedTools:[{type:r.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:r.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:r.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({email:1}),a.index({role:1}),a.index({emailVerificationToken:1}),a.index({"accounts.provider":1,"accounts.providerAccountId":1}),a.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},a.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(s=>s.provider!==e||s.providerAccountId!==t)};let o=i().models.User||i().model("User",a)},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(56037),i=s.n(r),n=s(36317);let a=new r.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:n.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({status:1,isActive:1}),a.index({category:1,status:1}),a.index({tags:1,status:1}),a.index({submittedBy:1}),a.index({launchDate:-1}),a.index({views:-1}),a.index({likes:-1}),a.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let o=i().models.Tool||i().model("Tool",a)},31098:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(56037),i=s.n(r);let n=new r.Schema({userId:{type:r.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},toolId:{type:r.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},type:{type:String,required:!0,enum:["launch_date_priority"],default:"launch_date_priority"},amount:{type:Number,required:[!0,"Amount is required"],min:[0,"Amount must be positive"]},currency:{type:String,required:!0,default:"CNY",enum:["CNY","USD"]},status:{type:String,required:!0,enum:["pending","completed","failed","cancelled","refunded"],default:"pending"},paymentMethod:{type:String,trim:!0},paymentIntentId:{type:String,trim:!0},paymentSessionId:{type:String,trim:!0},stripePaymentIntentId:{type:String,trim:!0},stripeCustomerId:{type:String,trim:!0},stripePaymentDetails:{paymentIntentId:String,amount:Number,currency:String,status:String,created:Date,failureReason:String},description:{type:String,required:[!0,"Description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},selectedLaunchDate:{type:Date,required:[!0,"Selected launch date is required"]},paidAt:{type:Date},cancelledAt:{type:Date},refundedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({userId:1,createdAt:-1}),n.index({toolId:1}),n.index({status:1}),n.index({paymentIntentId:1}),n.index({paymentSessionId:1}),n.index({stripePaymentIntentId:1}),n.index({stripeCustomerId:1}),n.virtual("user",{ref:"User",localField:"userId",foreignField:"_id",justOne:!0}),n.virtual("tool",{ref:"Tool",localField:"toolId",foreignField:"_id",justOne:!0}),n.methods.markAsPaid=function(){return this.status="completed",this.paidAt=new Date,this.save()},n.methods.markAsFailed=function(){return this.status="failed",this.save()},n.methods.cancel=function(){return this.status="cancelled",this.cancelledAt=new Date,this.save()},n.methods.refund=function(){return this.status="refunded",this.refundedAt=new Date,this.save()};let a=i().models.Order||i().model("Order",n)},36317:(e,t,s)=>{"use strict";s.d(t,{Bi:()=>i,PZ:()=>a,ut:()=>o,vK:()=>n});let r=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}];[...r.map(e=>({value:e.slug,label:e.name}))];let i=r.reduce((e,t)=>(e[t.slug]=t.name,e),{}),n=r.reduce((e,t)=>(e[t.slug]=t,e),{}),a=e=>n[e],o=r.map(e=>e.slug)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(56037),i=s.n(r);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=i().connect(n,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},79171:(e,t,s)=>{"use strict";s.d(t,{kX:()=>r});let r={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}};r.FREE_LAUNCH.description,r.FREE_LAUNCH.displayPrice,r.FREE_LAUNCH.features,r.PRIORITY_LAUNCH.description,r.PRIORITY_LAUNCH.displayPrice,r.PRIORITY_LAUNCH.features;let i={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}};i.FREE.value,i.FREE.label,i.FREEMIUM.value,i.FREEMIUM.label,i.PAID.value,i.PAID.label,i.FREE.value,i.FREE.label,i.FREEMIUM.value,i.FREEMIUM.label,i.PAID.value,i.PAID.label},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89765:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>D,routeModule:()=>v,serverHooks:()=>w,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>S});var r={};s.r(r),s.d(r,{GET:()=>I,PATCH:()=>x,POST:()=>h});var i=s(96559),n=s(48088),a=s(37719),o=s(32190),u=s(35426),c=s(75745),d=s(30762),l=s(31098),p=s(17063),m=s(12909),g=s(79171),y=s(56037),f=s.n(y);async function h(e,{params:t}){try{let s=await (0,u.getServerSession)(m.N);if(!s?.user?.email)return o.NextResponse.json({success:!1,message:"请先登录"},{status:401});await (0,c.A)();let{id:r}=await t,{launchOption:i,selectedDate:n}=await e.json();if(!f().Types.ObjectId.isValid(r))return o.NextResponse.json({success:!1,message:"无效的工具ID"},{status:400});if(!i||!n)return o.NextResponse.json({success:!1,message:"请选择发布选项和日期"},{status:400});if(!["free","paid"].includes(i))return o.NextResponse.json({success:!1,message:"无效的发布选项"},{status:400});let a=await p.A.findOne({email:s.user.email});if(!a)return o.NextResponse.json({success:!1,message:"用户不存在"},{status:404});let y=await d.A.findById(r);if(!y)return o.NextResponse.json({success:!1,message:"工具不存在"},{status:404});if(y.submittedBy!==a._id.toString())return o.NextResponse.json({success:!1,message:"您没有权限修改此工具"},{status:403});if("draft"!==y.status&&("pending"!==y.status||"free"!==y.launchOption||"paid"!==i))return o.NextResponse.json({success:!1,message:"此工具已经选择了发布日期"},{status:400});let h=new Date(n);if("free"===i){let e=new Date;e.setMonth(e.getMonth()+1),e.setHours(0,0,0,0);let t=new Date(h);if(t.setHours(0,0,0,0),t.getTime()<e.getTime())return o.NextResponse.json({success:!1,message:"免费选项只能选择一个月后的日期"},{status:400})}else{let e=new Date;if(e.setDate(e.getDate()+1),e.setHours(0,0,0,0),h<e)return o.NextResponse.json({success:!1,message:"付费选项最早只能选择明天的日期"},{status:400})}if("free"===i)return await d.A.findByIdAndUpdate(r,{$set:{launchDateSelected:!0,selectedLaunchDate:h,launchDate:h,launchOption:"free",paymentRequired:!1,status:"pending"}}),o.NextResponse.json({success:!0,data:{message:"发布日期设置成功，工具已进入审核队列"}});{let e=g.kX.PRIORITY_LAUNCH.stripeAmount,t=new l.A({userId:a._id,toolId:r,type:"launch_date_priority",amount:e,currency:g.kX.PRIORITY_LAUNCH.currency,status:"pending",description:`工具 "${y.name}" 优先发布服务`,selectedLaunchDate:h});await t.save();let s={launchDateSelected:!0,selectedLaunchDate:h,launchDate:h,launchOption:"paid",paymentRequired:!0,paymentAmount:e,paymentStatus:"pending",orderId:t._id.toString()};s.status="draft",await d.A.findByIdAndUpdate(r,{$set:s});let i=`/payment/checkout?orderId=${t._id}`;return o.NextResponse.json({success:!0,data:{orderId:t._id,paymentUrl:i,amount:e,message:"pending"===y.status?"升级订单创建成功，请完成支付":"订单创建成功，请完成支付"}})}}catch(e){return console.error("Launch date selection error:",e),o.NextResponse.json({success:!1,message:"服务器错误"},{status:500})}}async function I(e,{params:t}){try{let e=await (0,u.getServerSession)(m.N);if(!e?.user?.email)return o.NextResponse.json({success:!1,message:"请先登录"},{status:401});await (0,c.A)();let{id:s}=await t;if(!f().Types.ObjectId.isValid(s))return o.NextResponse.json({success:!1,message:"无效的工具ID"},{status:400});let r=await p.A.findOne({email:e.user.email});if(!r)return o.NextResponse.json({success:!1,message:"用户不存在"},{status:404});let i=await d.A.findById(s);if(!i)return o.NextResponse.json({success:!1,message:"工具不存在"},{status:404});if(i.submittedBy!==r._id.toString())return o.NextResponse.json({success:!1,message:"您没有权限访问此工具"},{status:403});let n=null;return i.orderId&&(n=await l.A.findById(i.orderId)),o.NextResponse.json({success:!0,data:{tool:{id:i._id,name:i.name,status:i.status,launchDateSelected:i.launchDateSelected,selectedLaunchDate:i.selectedLaunchDate,launchOption:i.launchOption,paymentRequired:i.paymentRequired,paymentStatus:i.paymentStatus},order:n}})}catch(e){return console.error("Get launch date info error:",e),o.NextResponse.json({success:!1,message:"服务器错误"},{status:500})}}async function x(e,{params:t}){try{let s=await (0,u.getServerSession)(m.N);if(!s?.user?.email)return o.NextResponse.json({success:!1,message:"请先登录"},{status:401});await (0,c.A)();let{id:r}=await t,{selectedDate:i}=await e.json();if(!f().Types.ObjectId.isValid(r))return o.NextResponse.json({success:!1,message:"无效的工具ID"},{status:400});if(!i)return o.NextResponse.json({success:!1,message:"请选择发布日期"},{status:400});let n=await p.A.findOne({email:s.user.email});if(!n)return o.NextResponse.json({success:!1,message:"用户不存在"},{status:404});let a=await d.A.findById(r);if(!a)return o.NextResponse.json({success:!1,message:"工具不存在"},{status:404});if(a.submittedBy!==n._id.toString())return o.NextResponse.json({success:!1,message:"您没有权限修改此工具"},{status:403});if(!["pending","approved"].includes(a.status))return o.NextResponse.json({success:!1,message:"当前状态不允许修改发布日期"},{status:400});let g=new Date;if("approved"===a.status&&a.launchDate&&new Date(a.launchDate)<=g)return o.NextResponse.json({success:!1,message:"工具已发布，无法修改发布日期"},{status:400});let y=new Date(i);if("paid"===a.launchOption){let e=new Date;if(e.setDate(e.getDate()+1),e.setHours(0,0,0,0),y<e)return o.NextResponse.json({success:!1,message:"付费用户发布日期最早只能选择明天"},{status:400})}else{let e=new Date;e.setMonth(e.getMonth()+1),e.setHours(0,0,0,0);let t=new Date(y);if(t.setHours(0,0,0,0),t.getTime()<e.getTime())return o.NextResponse.json({success:!1,message:"免费用户只能选择一个月后的日期"},{status:400})}return await d.A.findByIdAndUpdate(r,{$set:{selectedLaunchDate:y,launchDate:y}}),a.orderId&&await l.A.findByIdAndUpdate(a.orderId,{$set:{selectedLaunchDate:y}}),o.NextResponse.json({success:!0,data:{message:"发布日期修改成功",selectedLaunchDate:y}})}catch(e){return console.error("Update launch date error:",e),o.NextResponse.json({success:!1,message:"服务器错误"},{status:500})}}let v=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/tools/[id]/launch-date/route",pathname:"/api/tools/[id]/launch-date",filename:"route",bundlePath:"app/api/tools/[id]/launch-date/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:A,workUnitAsyncStorage:S,serverHooks:w}=v;function D(){return(0,a.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:S})}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,580,4999,3136],()=>s(89765));module.exports=r})();