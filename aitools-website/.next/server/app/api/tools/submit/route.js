(()=>{var e={};e.id=1490,e.ids=[1490],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7747:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>f,serverHooks:()=>x,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>v});var i={};r.r(i),r.d(i,{GET:()=>g,POST:()=>m});var s=r(96559),n=r(48088),a=r(37719),o=r(32190),c=r(19854),u=r(12909),l=r(75745),d=r(30762),p=r(17063);async function m(e){try{let t=await (0,c.getServerSession)(u.N);if(!t?.user?.email)return o.NextResponse.json({success:!1,message:"请先登录"},{status:401});await (0,l.A)();let r=await p.A.findOne({email:t.user.email});if(!r)return o.NextResponse.json({success:!1,message:"用户不存在"},{status:404});let{name:i,tagline:s,description:n,longDescription:a,website:m,logo:g,category:f,tags:y,pricing:v,pricingDetails:x,screenshots:h}=await e.json();if(!i||!n||!m||!f||!v)return o.NextResponse.json({success:!1,message:"请填写所有必填字段"},{status:400});if(!/^https?:\/\/.+/.test(m))return o.NextResponse.json({success:!1,message:"请输入有效的网站URL"},{status:400});if(!["text-generation","image-generation","video-generation","audio-generation","code-generation","data-analysis","productivity","design","marketing","education","research","other"].includes(f))return o.NextResponse.json({success:!1,message:"无效的分类"},{status:400});if(!["free","freemium","paid"].includes(v))return o.NextResponse.json({success:!1,message:"无效的定价模式"},{status:400});if(await d.A.findOne({name:{$regex:RegExp(`^${i}$`,"i")},isActive:!0}))return o.NextResponse.json({success:!1,message:"该工具名称已存在"},{status:409});let w=new d.A({name:i.trim(),tagline:s?.trim(),description:n.trim(),longDescription:a?.trim(),website:m.trim(),logo:g?.trim(),category:f,tags:y?y.map(e=>e.trim().toLowerCase()).filter(Boolean):[],pricing:v,pricingDetails:x?.trim(),screenshots:h?h.map(e=>e.trim()).filter(Boolean):[],submittedBy:r._id.toString(),submittedAt:new Date,status:"draft",launchDateSelected:!1,paymentRequired:!1,views:0,likes:0,likedBy:[],isActive:!0});return await w.save(),r.submittedTools.includes(w._id.toString())||(r.submittedTools.push(w._id.toString()),await r.save()),o.NextResponse.json({success:!0,data:{toolId:w._id,message:"工具信息保存成功，请选择发布日期"}},{status:201})}catch(e){return console.error("Submit tool error:",e),o.NextResponse.json({success:!1,message:"服务器错误"},{status:500})}}async function g(e){try{let t=await (0,c.getServerSession)(u.N);if(!t?.user?.email)return o.NextResponse.json({success:!1,message:"请先登录"},{status:401});await (0,l.A)();let r=await p.A.findOne({email:t.user.email});if(!r)return o.NextResponse.json({success:!1,message:"用户不存在"},{status:404});let{searchParams:i}=new URL(e.url),s=parseInt(i.get("page")||"1"),n=parseInt(i.get("limit")||"10"),a=(s-1)*n,m=await d.A.find({submittedBy:r._id.toString(),isActive:!0}).sort({submittedAt:-1}).skip(a).limit(n),g=await d.A.countDocuments({submittedBy:r._id.toString(),isActive:!0});return o.NextResponse.json({success:!0,data:{tools:m,pagination:{page:s,limit:n,total:g,pages:Math.ceil(g/n)}}})}catch(e){return console.error("Get user tools error:",e),o.NextResponse.json({success:!1,message:"服务器错误"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/tools/submit/route",pathname:"/api/tools/submit",filename:"route",bundlePath:"app/api/tools/submit/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:y,workUnitAsyncStorage:v,serverHooks:x}=f;function h(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:v})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>c});var i=r(36344),s=r(65752),n=r(13581),a=r(75745),o=r(17063);let c={providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,n.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,a.A)();let t=await o.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,s]=r.split(":");if(i!==e.token||s!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;await (0,a.A)();try{let i=await o.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new o.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),e.id=i._id.toString(),e.role=i.role,!0}catch(e){return console.error("Sign in error:",e),!1}},async jwt({token:e,user:t}){if(t)e.userId=t.id,e.role=t.role||"user";else if(e.userId)try{await (0,a.A)();let t=await o.A.findById(e.userId);t&&(e.role=t.role)}catch(e){console.error("Error fetching user role:",e)}return e},session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),s=r.n(i);let n=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),a=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[n],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({email:1}),a.index({role:1}),a.index({emailVerificationToken:1}),a.index({"accounts.provider":1,"accounts.providerAccountId":1}),a.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},a.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let o=s().models.User||s().model("User",a)},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var s=r(12269);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var o=s?Object.getOwnPropertyDescriptor(e,n):null;o&&(o.get||o.set)?Object.defineProperty(i,n,o):i[n]=e[n]}return i.default=e,r&&r.set(e,i),i}(r(35426));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),s=r.n(i),n=r(36317);let a=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:n.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({status:1,isActive:1}),a.index({category:1,status:1}),a.index({tags:1,status:1}),a.index({submittedBy:1}),a.index({launchDate:-1}),a.index({views:-1}),a.index({likes:-1}),a.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let o=s().models.Tool||s().model("Tool",a)},36317:(e,t,r)=>{"use strict";r.d(t,{Bi:()=>s,PZ:()=>a,ut:()=>o,vK:()=>n});let i=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}];[...i.map(e=>({value:e.slug,label:e.name}))];let s=i.reduce((e,t)=>(e[t.slug]=t.name,e),{}),n=i.reduce((e,t)=>(e[t.slug]=t,e),{}),a=e=>n[e],o=i.map(e=>e.slug)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),s=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=s().connect(n,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,580,4999,3136],()=>r(7747));module.exports=i})();