(()=>{var e={};e.id=6935,e.ids=[6706,6935],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39020:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>R,routeModule:()=>p,serverHooks:()=>E,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{POST:()=>u});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),c=r(96706);async function u(e){try{let{amount:t,currency:r="cny"}=await e.json();if(!t||t<=0)return i.NextResponse.json({success:!1,message:"无效的金额"},{status:400});let s=await (0,c.f)(t,r,{test:"true",description:"Stripe集成测试支付"});return i.NextResponse.json({success:!0,clientSecret:s.client_secret,paymentIntentId:s.id})}catch(e){return console.error("Error creating test payment intent:",e),i.NextResponse.json({success:!1,message:"创建支付失败"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/test/create-payment-intent/route",pathname:"/api/test/create-payment-intent",filename:"route",bundlePath:"app/api/test/create-payment-intent/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:E}=p;function R(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79171:(e,t,r)=>{"use strict";r.d(t,{kX:()=>s});let s={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}};s.FREE_LAUNCH.description,s.FREE_LAUNCH.displayPrice,s.FREE_LAUNCH.features,s.PRIORITY_LAUNCH.description,s.PRIORITY_LAUNCH.displayPrice,s.PRIORITY_LAUNCH.features;let a={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}};a.FREE.value,a.FREE.label,a.FREEMIUM.value,a.FREEMIUM.label,a.PAID.value,a.PAID.label,a.FREE.value,a.FREE.label,a.FREEMIUM.value,a.FREEMIUM.label,a.PAID.value,a.PAID.label},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96706:(e,t,r)=>{"use strict";r.d(t,{ZW:()=>u,bw:()=>c,f:()=>o,stripe:()=>n});var s=r(97877),a=r(79171);let n=new s.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-12-18.acacia",typescript:!0});async function o(e,t="cny",r={}){try{return await n.paymentIntents.create({amount:e,currency:t,metadata:r,automatic_payment_methods:{enabled:!0}})}catch(e){throw console.error("Error creating payment intent:",e),Error("Failed to create payment intent")}}async function i(e,t,r={}){try{return await n.customers.create({email:e,name:t,metadata:r})}catch(e){throw console.error("Error creating Stripe customer:",e),Error("Failed to create customer")}}async function c(e,t,r={}){try{let s=await n.customers.list({email:e,limit:1});if(s.data.length>0)return s.data[0];return await i(e,t,r)}catch(e){throw console.error("Error getting or creating Stripe customer:",e),Error("Failed to get or create customer")}}function u(e,t,r){try{return n.webhooks.constructEvent(e,t,r)}catch(e){throw console.error("Error constructing webhook event:",e),Error("Invalid webhook signature")}}a.kX.PRIORITY_LAUNCH.productName,a.kX.PRIORITY_LAUNCH.stripeAmount,a.kX.PRIORITY_LAUNCH.stripeCurrency,a.kX.PRIORITY_LAUNCH.description,a.kX.PRIORITY_LAUNCH.features}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,7877],()=>r(39020));module.exports=s})();