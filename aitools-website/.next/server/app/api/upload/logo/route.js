(()=>{var e={};e.id=2673,e.ids=[2673],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>u});var i=t(36344),s=t(65752),o=t(13581),n=t(75745),a=t(17063);let u={providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,o.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,n.A)();let r=await a.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!r)return null;let t=r.emailVerificationToken;if(!t||!t.includes(":"))return null;let[i,s]=t.split(":");if(i!==e.token||s!==e.code)return null;return r.emailVerified=!0,r.emailVerificationToken=void 0,r.emailVerificationExpires=void 0,r.lastLoginAt=new Date,r.accounts.some(e=>"email"===e.provider)||r.accounts.push({provider:"email",providerId:"email",providerAccountId:r.email}),await r.save(),{id:r._id.toString(),email:r.email,name:r.name,image:r.avatar,role:r.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:r,profile:t}){if(r?.provider==="email-code")return!0;await (0,n.A)();try{let i=await a.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new a.A({email:e.email,name:e.name||t?.name||"User",avatar:e.image||t?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),r&&"email-code"!==r.provider&&(i.addAccount({provider:r.provider,providerId:r.provider,providerAccountId:r.providerAccountId||r.id||"",accessToken:r.access_token,refreshToken:r.refresh_token,expiresAt:r.expires_at?new Date(1e3*r.expires_at):void 0}),await i.save()),e.id=i._id.toString(),e.role=i.role,!0}catch(e){return console.error("Sign in error:",e),!1}},async jwt({token:e,user:r}){if(r)e.userId=r.id,e.role=r.role||"user";else if(e.userId)try{await (0,n.A)();let r=await a.A.findById(e.userId);r&&(e.role=r.role)}catch(e){console.error("Error fetching user role:",e)}return e},session:async({session:e,token:r})=>(r&&e.user&&(e.user.id=r.userId,e.user.role=r.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var i=t(56037),s=t.n(i);let o=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),n=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[o],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({email:1}),n.index({role:1}),n.index({emailVerificationToken:1}),n.index({"accounts.provider":1,"accounts.providerAccountId":1}),n.methods.addAccount=function(e){let r=this.accounts.find(r=>r.provider===e.provider&&r.providerAccountId===e.providerAccountId);r?Object.assign(r,e):this.accounts.push(e)},n.methods.removeAccount=function(e,r){this.accounts=this.accounts.filter(t=>t.provider!==e||t.providerAccountId!==r)};let a=s().models.User||s().model("User",n)},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var s=t(12269);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(r);if(t&&t.has(e))return t.get(e);var i={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var a=s?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(i,o,a):i[o]=e[o]}return i.default=e,t&&t.set(e,i),i}(t(35426));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36842:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>m,serverHooks:()=>v,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var i={};t.r(i),t.d(i,{POST:()=>d});var s=t(96559),o=t(48088),n=t(37719),a=t(32190);let u=require("fs/promises");var c=t(33873),l=t(19854),p=t(12909);async function d(e){try{let r=await (0,l.getServerSession)(p.N);if(!r?.user?.email)return a.NextResponse.json({success:!1,message:"请先登录"},{status:401});let t=(await e.formData()).get("logo");if(!t)return a.NextResponse.json({success:!1,message:"请选择要上传的文件"},{status:400});if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(t.type))return a.NextResponse.json({success:!1,message:"只支持 JPEG、PNG、GIF、WebP 格式的图片"},{status:400});if(t.size>5242880)return a.NextResponse.json({success:!1,message:"文件大小不能超过 5MB"},{status:400});let i=Date.now(),s=Math.random().toString(36).substring(2,15),o=t.name.split(".").pop(),n=`logo_${i}_${s}.${o}`,d=(0,c.join)(process.cwd(),"public","uploads","logos");try{await (0,u.mkdir)(d,{recursive:!0})}catch(e){}let m=(0,c.join)(d,n),f=await t.arrayBuffer(),g=Buffer.from(f);await (0,u.writeFile)(m,g);let v=`/uploads/logos/${n}`;return a.NextResponse.json({success:!0,data:{url:v,filename:n,size:t.size,type:t.type}})}catch(e){return console.error("Upload error:",e),a.NextResponse.json({success:!1,message:"文件上传失败"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/upload/logo/route",pathname:"/api/upload/logo",filename:"route",bundlePath:"app/api/upload/logo/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:v}=m;function y(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var i=t(56037),s=t.n(i);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let a=async function(){if(n.conn)return n.conn;n.promise||(n.promise=s().connect(o,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[4243,580,4999,3136],()=>t(36842));module.exports=i})();