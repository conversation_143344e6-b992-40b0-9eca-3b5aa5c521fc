exports.id=7877,exports.ids=[7877],exports.modules={1328:e=>{"use strict";e.exports=EvalError},1441:e=>{"use strict";e.exports=ReferenceError},1700:(e,t,r)=>{"use strict";var o=r(35580);e.exports=function(e){return o(e)||0===e?e:e<0?-1:1}},3489:e=>{"use strict";e.exports=Math.abs},3495:e=>{"use strict";e.exports=Math.max},3922:e=>{"use strict";var t=Object.defineProperty||!1;if(t)try{t({},"a",{value:1})}catch(e){t=!1}e.exports=t},4957:e=>{"use strict";e.exports=Math.round},9581:(e,t,r)=>{"use strict";var o,n=r(70649),i=r(54198);try{o=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var a=!!o&&i&&i(Object.prototype,"__proto__"),s=Object,l=s.getPrototypeOf;e.exports=a&&"function"==typeof a.get?n([a.get]):"function"==typeof l&&function(e){return l(null==e?e:s(e))}},18685:(e,t,r)=>{"use strict";var o=r(87897),n=r(85681),i=r(44640),a=Object.prototype.hasOwnProperty,s={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},l=Array.isArray,u=Array.prototype.push,c=function(e,t){u.apply(e,l(t)?t:[t])},d=Date.prototype.toISOString,h=i.default,p={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:n.encode,encodeValuesOnly:!1,filter:void 0,format:h,formatter:i.formatters[h],indices:!1,serializeDate:function(e){return d.call(e)},skipNulls:!1,strictNullHandling:!1},m={},f=function e(t,r,i,a,s,u,d,h,f,y,v,P,g,T,_,E,S,b){for(var O,x,w=t,A=b,R=0,C=!1;void 0!==(A=A.get(m))&&!C;){var G=A.get(t);if(R+=1,void 0!==G)if(G===R)throw RangeError("Cyclic object value");else C=!0;void 0===A.get(m)&&(R=0)}if("function"==typeof y?w=y(r,w):w instanceof Date?w=g(w):"comma"===i&&l(w)&&(w=n.maybeMap(w,function(e){return e instanceof Date?g(e):e})),null===w){if(u)return f&&!E?f(r,p.encoder,S,"key",T):r;w=""}if("string"==typeof(O=w)||"number"==typeof O||"boolean"==typeof O||"symbol"==typeof O||"bigint"==typeof O||n.isBuffer(w))return f?[_(E?r:f(r,p.encoder,S,"key",T))+"="+_(f(w,p.encoder,S,"value",T))]:[_(r)+"="+_(String(w))];var I=[];if(void 0===w)return I;if("comma"===i&&l(w))E&&f&&(w=n.maybeMap(w,f)),x=[{value:w.length>0?w.join(",")||null:void 0}];else if(l(y))x=y;else{var j=Object.keys(w);x=v?j.sort(v):j}var k=h?String(r).replace(/\./g,"%2E"):String(r),D=a&&l(w)&&1===w.length?k+"[]":k;if(s&&l(w)&&0===w.length)return D+"[]";for(var N=0;N<x.length;++N){var M=x[N],F="object"==typeof M&&M&&void 0!==M.value?M.value:w[M];if(!d||null!==F){var q=P&&h?String(M).replace(/\./g,"%2E"):String(M),U=l(w)?"function"==typeof i?i(D,q):D:D+(P?"."+q:"["+q+"]");b.set(t,R);var L=o();L.set(m,b),c(I,e(F,U,i,a,s,u,d,h,"comma"===i&&E&&l(w)?null:f,y,v,P,g,T,_,E,S,L))}}return I},y=function(e){if(!e)return p;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");var t,r=e.charset||p.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var o=i.default;if(void 0!==e.format){if(!a.call(i.formatters,e.format))throw TypeError("Unknown format option provided.");o=e.format}var n=i.formatters[o],u=p.filter;if(("function"==typeof e.filter||l(e.filter))&&(u=e.filter),t=e.arrayFormat in s?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":p.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");var c=void 0===e.allowDots?!0===e.encodeDotInKeys||p.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:p.addQueryPrefix,allowDots:c,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:p.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:p.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?p.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:p.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:p.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:p.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:p.encodeValuesOnly,filter:u,format:o,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:p.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:p.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:p.strictNullHandling}};e.exports=function(e,t){var r,n,i=e,a=y(t);"function"==typeof a.filter?i=(0,a.filter)("",i):l(a.filter)&&(r=a.filter);var u=[];if("object"!=typeof i||null===i)return"";var d=s[a.arrayFormat],h="comma"===d&&a.commaRoundTrip;r||(r=Object.keys(i)),a.sort&&r.sort(a.sort);for(var p=o(),m=0;m<r.length;++m){var v=r[m],P=i[v];a.skipNulls&&null===P||c(u,f(P,v,d,h,a.allowEmptyArrays,a.strictNullHandling,a.skipNulls,a.encodeDotInKeys,a.encode?a.encoder:null,a.filter,a.sort,a.allowDots,a.serializeDate,a.format,a.formatter,a.encodeValuesOnly,a.charset,p))}var g=u.join(a.delimiter),T=!0===a.addQueryPrefix?"?":"";return a.charsetSentinel&&("iso-8859-1"===a.charset?T+="utf8=%26%2310003%3B&":T+="utf8=%E2%9C%93&"),g.length>0?T+g:""}},19118:(e,t,r)=>{var o="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,i=o&&n&&"function"==typeof n.get?n.get:null,a=o&&Map.prototype.forEach,s="function"==typeof Set&&Set.prototype,l=Object.getOwnPropertyDescriptor&&s?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=s&&l&&"function"==typeof l.get?l.get:null,c=s&&Set.prototype.forEach,d="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,h="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,p="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,m=Boolean.prototype.valueOf,f=Object.prototype.toString,y=Function.prototype.toString,v=String.prototype.match,P=String.prototype.slice,g=String.prototype.replace,T=String.prototype.toUpperCase,_=String.prototype.toLowerCase,E=RegExp.prototype.test,S=Array.prototype.concat,b=Array.prototype.join,O=Array.prototype.slice,x=Math.floor,w="function"==typeof BigInt?BigInt.prototype.valueOf:null,A=Object.getOwnPropertySymbols,R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,C="function"==typeof Symbol&&"object"==typeof Symbol.iterator,G="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===C?"object":"symbol")?Symbol.toStringTag:null,I=Object.prototype.propertyIsEnumerable,j=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function k(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||E.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-x(-e):x(e);if(o!==e){var n=String(o),i=P.call(t,n.length+1);return g.call(n,r,"$&_")+"."+g.call(g.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return g.call(t,r,"$&_")}var D=r(75138),N=D.custom,M=z(N)?N:null,F={__proto__:null,double:'"',single:"'"},q={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function U(e,t,r){var o=F[r.quoteStyle||t];return o+e+o}function L(e){return!G||!("object"==typeof e&&(G in e||void 0!==e[G]))}function H(e){return"[object Array]"===K(e)&&L(e)}function $(e){return"[object RegExp]"===K(e)&&L(e)}function z(e){if(C)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!R)return!1;try{return R.call(e),!0}catch(e){}return!1}e.exports=function e(t,r,o,n){var s,l,f,T,E,x=r||{};if(W(x,"quoteStyle")&&!W(F,x.quoteStyle))throw TypeError('option "quoteStyle" must be "single" or "double"');if(W(x,"maxStringLength")&&("number"==typeof x.maxStringLength?x.maxStringLength<0&&x.maxStringLength!==1/0:null!==x.maxStringLength))throw TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var A=!W(x,"customInspect")||x.customInspect;if("boolean"!=typeof A&&"symbol"!==A)throw TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(W(x,"indent")&&null!==x.indent&&"	"!==x.indent&&!(parseInt(x.indent,10)===x.indent&&x.indent>0))throw TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(W(x,"numericSeparator")&&"boolean"!=typeof x.numericSeparator)throw TypeError('option "numericSeparator", if provided, must be `true` or `false`');var N=x.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return function e(t,r){if(t.length>r.maxStringLength){var o=t.length-r.maxStringLength;return e(P.call(t,0,r.maxStringLength),r)+("... "+o)+" more character"+(o>1?"s":"")}var n=q[r.quoteStyle||"single"];return n.lastIndex=0,U(g.call(g.call(t,n,"\\$1"),/[\x00-\x1f]/g,J),"single",r)}(t,x);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var B=String(t);return N?k(t,B):B}if("bigint"==typeof t){var et=String(t)+"n";return N?k(t,et):et}var er=void 0===x.depth?5:x.depth;if(void 0===o&&(o=0),o>=er&&er>0&&"object"==typeof t)return H(t)?"[Array]":"[Object]";var eo=function(e,t){var r;if("	"===e.indent)r="	";else{if("number"!=typeof e.indent||!(e.indent>0))return null;r=b.call(Array(e.indent+1)," ")}return{base:r,prev:b.call(Array(t+1),r)}}(x,o);if(void 0===n)n=[];else if(V(n,t)>=0)return"[Circular]";function en(t,r,i){if(r&&(n=O.call(n)).push(r),i){var a={depth:x.depth};return W(x,"quoteStyle")&&(a.quoteStyle=x.quoteStyle),e(t,a,o+1,n)}return e(t,x,o+1,n)}if("function"==typeof t&&!$(t)){var ei=function(e){if(e.name)return e.name;var t=v.call(y.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),ea=ee(t,en);return"[Function"+(ei?": "+ei:" (anonymous)")+"]"+(ea.length>0?" { "+b.call(ea,", ")+" }":"")}if(z(t)){var es=C?g.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):R.call(t);return"object"!=typeof t||C?es:Q(es)}if((el=t)&&"object"==typeof el&&("undefined"!=typeof HTMLElement&&el instanceof HTMLElement||"string"==typeof el.nodeName&&"function"==typeof el.getAttribute)){for(var el,eu,ec="<"+_.call(String(t.nodeName)),ed=t.attributes||[],eh=0;eh<ed.length;eh++){ec+=" "+ed[eh].name+"="+U((eu=ed[eh].value,g.call(String(eu),/"/g,"&quot;")),"double",x)}return ec+=">",t.childNodes&&t.childNodes.length&&(ec+="..."),ec+="</"+_.call(String(t.nodeName))+">"}if(H(t)){if(0===t.length)return"[]";var ep=ee(t,en);return eo&&!function(e){for(var t=0;t<e.length;t++)if(V(e[t],"\n")>=0)return!1;return!0}(ep)?"["+Z(ep,eo)+"]":"[ "+b.call(ep,", ")+" ]"}if("[object Error]"===K(s=t)&&L(s)){var em=ee(t,en);return"cause"in Error.prototype||!("cause"in t)||I.call(t,"cause")?0===em.length?"["+String(t)+"]":"{ ["+String(t)+"] "+b.call(em,", ")+" }":"{ ["+String(t)+"] "+b.call(S.call("[cause]: "+en(t.cause),em),", ")+" }"}if("object"==typeof t&&A){if(M&&"function"==typeof t[M]&&D)return D(t,{depth:er-o});else if("symbol"!==A&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!i||!e||"object"!=typeof e)return!1;try{i.call(e);try{u.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var ef=[];return a&&a.call(t,function(e,r){ef.push(en(r,t,!0)+" => "+en(e,t))}),Y("Map",i.call(t),ef,eo)}if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e);try{i.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var ey=[];return c&&c.call(t,function(e){ey.push(en(e,t))}),Y("Set",u.call(t),ey,eo)}if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{h.call(e,h)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return X("WeakMap");if(function(e){if(!h||!e||"object"!=typeof e)return!1;try{h.call(e,h);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return X("WeakSet");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{return p.call(e),!0}catch(e){}return!1}(t))return X("WeakRef");if("[object Number]"===K(l=t)&&L(l))return Q(en(Number(t)));if(function(e){if(!e||"object"!=typeof e||!w)return!1;try{return w.call(e),!0}catch(e){}return!1}(t))return Q(en(w.call(t)));if("[object Boolean]"===K(f=t)&&L(f))return Q(m.call(t));if("[object String]"===K(T=t)&&L(T))return Q(en(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||"undefined"!=typeof global&&t===global)return"{ [object globalThis] }";if(!("[object Date]"===K(E=t)&&L(E))&&!$(t)){var ev=ee(t,en),eP=j?j(t)===Object.prototype:t instanceof Object||t.constructor===Object,eg=t instanceof Object?"":"null prototype",eT=!eP&&G&&Object(t)===t&&G in t?P.call(K(t),8,-1):eg?"Object":"",e_=(eP||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(eT||eg?"["+b.call(S.call([],eT||[],eg||[]),": ")+"] ":"");return 0===ev.length?e_+"{}":eo?e_+"{"+Z(ev,eo)+"}":e_+"{ "+b.call(ev,", ")+" }"}return String(t)};var B=Object.prototype.hasOwnProperty||function(e){return e in this};function W(e,t){return B.call(e,t)}function K(e){return f.call(e)}function V(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return -1}function J(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+T.call(t.toString(16))}function Q(e){return"Object("+e+")"}function X(e){return e+" { ? }"}function Y(e,t,r,o){return e+" ("+t+") {"+(o?Z(r,o):b.call(r,", "))+"}"}function Z(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+b.call(e,","+r)+"\n"+t.prev}function ee(e,t){var r,o=H(e),n=[];if(o){n.length=e.length;for(var i=0;i<e.length;i++)n[i]=W(e,i)?t(e[i],e):""}var a="function"==typeof A?A(e):[];if(C){r={};for(var s=0;s<a.length;s++)r["$"+a[s]]=a[s]}for(var l in e)if(W(e,l)&&(!o||String(Number(l))!==l||!(l<e.length)))if(C&&r["$"+l]instanceof Symbol)continue;else E.call(/[^\w$]/,l)?n.push(t(l,e)+": "+t(e[l],e)):n.push(l+": "+t(e[l],e));if("function"==typeof A)for(var u=0;u<a.length;u++)I.call(e,a[u])&&n.push("["+t(a[u])+"]: "+t(e[a[u]],e));return n}},23632:e=>{"use strict";var t=Object.prototype.toString,r=Math.max,o=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r},n=function(e,t){for(var r=[],o=t||0,n=0;o<e.length;o+=1,n+=1)r[n]=e[o];return r},i=function(e,t){for(var r="",o=0;o<e.length;o+=1)r+=e[o],o+1<e.length&&(r+=t);return r};e.exports=function(e){var a,s=this;if("function"!=typeof s||"[object Function]"!==t.apply(s))throw TypeError("Function.prototype.bind called on incompatible "+s);for(var l=n(arguments,1),u=r(0,s.length-l.length),c=[],d=0;d<u;d++)c[d]="$"+d;if(a=Function("binder","return function ("+i(c,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof a){var t=s.apply(this,o(l,arguments));return Object(t)===t?t:this}return s.apply(e,o(l,arguments))}),s.prototype){var h=function(){};h.prototype=s.prototype,a.prototype=new h,h.prototype=null}return a}},24513:e=>{"use strict";e.exports=RangeError},25586:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},25907:(e,t,r)=>{"use strict";var o=r(42545),n=r(72595),i=r(9581);e.exports=o?function(e){return o(e)}:n?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return n(e)}:i?function(e){return i(e)}:null},33376:(e,t,r)=>{"use strict";var o=r(18685),n=r(85903);e.exports={formats:r(44640),parse:n,stringify:o}},35580:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!=e}},36456:(e,t,r)=>{"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=r(92590);e.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},39703:e=>{"use strict";e.exports=Math.floor},42257:e=>{"use strict";e.exports=SyntaxError},42545:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},42690:(e,t,r)=>{"use strict";var o=r(81330),n=r(45177),i=r(19118),a=r(50122),s=r(88486),l=o("%WeakMap%",!0),u=n("WeakMap.prototype.get",!0),c=n("WeakMap.prototype.set",!0),d=n("WeakMap.prototype.has",!0),h=n("WeakMap.prototype.delete",!0);e.exports=l?function(){var e,t,r={assert:function(e){if(!r.has(e))throw new s("Side channel does not contain "+i(e))},delete:function(r){if(l&&r&&("object"==typeof r||"function"==typeof r)){if(e)return h(e,r)}else if(a&&t)return t.delete(r);return!1},get:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?u(e,r):t&&t.get(r)},has:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?d(e,r):!!t&&t.has(r)},set:function(r,o){l&&r&&("object"==typeof r||"function"==typeof r)?(e||(e=new l),c(e,r,o)):a&&(t||(t=a()),t.set(r,o))}};return r}:a},44332:e=>{"use strict";e.exports=Error},44640:e=>{"use strict";var t=String.prototype.replace,r=/%20/g,o={RFC1738:"RFC1738",RFC3986:"RFC3986"};e.exports={default:o.RFC3986,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:o.RFC1738,RFC3986:o.RFC3986}},45149:e=>{"use strict";e.exports=Function.prototype.call},45177:(e,t,r)=>{"use strict";var o=r(81330),n=r(70649),i=n([o("%String.prototype.indexOf%")]);e.exports=function(e,t){var r=o(e,!!t);return"function"==typeof r&&i(e,".prototype.")>-1?n([r]):r}},46164:(e,t,r)=>{"use strict";var o=r(23632);e.exports=Function.prototype.bind||o},47284:(e,t,r)=>{"use strict";var o=Function.prototype.call,n=Object.prototype.hasOwnProperty;e.exports=r(46164).call(o,n)},50095:e=>{"use strict";e.exports=Math.pow},50122:(e,t,r)=>{"use strict";var o=r(81330),n=r(45177),i=r(19118),a=r(88486),s=o("%Map%",!0),l=n("Map.prototype.get",!0),u=n("Map.prototype.set",!0),c=n("Map.prototype.has",!0),d=n("Map.prototype.delete",!0),h=n("Map.prototype.size",!0);e.exports=!!s&&function(){var e,t={assert:function(e){if(!t.has(e))throw new a("Side channel does not contain "+i(e))},delete:function(t){if(e){var r=d(e,t);return 0===h(e)&&(e=void 0),r}return!1},get:function(t){if(e)return l(e,t)},has:function(t){return!!e&&c(e,t)},set:function(t,r){e||(e=new s),u(e,t,r)}};return t}},54198:(e,t,r)=>{"use strict";var o=r(25586);if(o)try{o([],"length")}catch(e){o=null}e.exports=o},58803:(e,t,r)=>{"use strict";var o=r(46164),n=r(93377),i=r(45149);e.exports=r(93926)||o.call(i,n)},70649:(e,t,r)=>{"use strict";var o=r(46164),n=r(88486),i=r(45149),a=r(58803);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new n("a function is required");return a(o,i,e)}},72595:(e,t,r)=>{"use strict";e.exports=r(98363).getPrototypeOf||null},75138:(e,t,r)=>{e.exports=r(28354).inspect},78022:e=>{"use strict";e.exports=URIError},81330:(e,t,r)=>{"use strict";var o,n=r(98363),i=r(44332),a=r(1328),s=r(24513),l=r(1441),u=r(42257),c=r(88486),d=r(78022),h=r(3489),p=r(39703),m=r(3495),f=r(96961),y=r(50095),v=r(4957),P=r(1700),g=Function,T=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},_=r(54198),E=r(3922),S=function(){throw new c},b=_?function(){try{return arguments.callee,S}catch(e){try{return _(arguments,"callee").get}catch(e){return S}}}():S,O=r(36456)(),x=r(25907),w=r(72595),A=r(42545),R=r(93377),C=r(45149),G={},I="undefined"!=typeof Uint8Array&&x?x(Uint8Array):o,j={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":O&&x?x([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":G,"%AsyncGenerator%":G,"%AsyncGeneratorFunction%":G,"%AsyncIteratorPrototype%":G,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":i,"%eval%":eval,"%EvalError%":a,"%Float16Array%":"undefined"==typeof Float16Array?o:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":G,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":O&&x?x(x([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&O&&x?x(new Map()[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":n,"%Object.getOwnPropertyDescriptor%":_,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":s,"%ReferenceError%":l,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&O&&x?x(new Set()[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":O&&x?x(""[Symbol.iterator]()):o,"%Symbol%":O?Symbol:o,"%SyntaxError%":u,"%ThrowTypeError%":b,"%TypedArray%":I,"%TypeError%":c,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":d,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet,"%Function.prototype.call%":C,"%Function.prototype.apply%":R,"%Object.defineProperty%":E,"%Object.getPrototypeOf%":w,"%Math.abs%":h,"%Math.floor%":p,"%Math.max%":m,"%Math.min%":f,"%Math.pow%":y,"%Math.round%":v,"%Math.sign%":P,"%Reflect.getPrototypeOf%":A};if(x)try{null.error}catch(e){var k=x(x(e));j["%Error.prototype%"]=k}var D=function e(t){var r;if("%AsyncFunction%"===t)r=T("async function () {}");else if("%GeneratorFunction%"===t)r=T("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=T("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&x&&(r=x(n.prototype))}return j[t]=r,r},N={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},M=r(46164),F=r(47284),q=M.call(C,Array.prototype.concat),U=M.call(R,Array.prototype.splice),L=M.call(C,String.prototype.replace),H=M.call(C,String.prototype.slice),$=M.call(C,RegExp.prototype.exec),z=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,B=/\\(\\)?/g,W=function(e){var t=H(e,0,1),r=H(e,-1);if("%"===t&&"%"!==r)throw new u("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new u("invalid intrinsic syntax, expected opening `%`");var o=[];return L(e,z,function(e,t,r,n){o[o.length]=r?L(n,B,"$1"):t||e}),o},K=function(e,t){var r,o=e;if(F(N,o)&&(o="%"+(r=N[o])[0]+"%"),F(j,o)){var n=j[o];if(n===G&&(n=D(o)),void 0===n&&!t)throw new c("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new u("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new c("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new c('"allowMissing" argument must be a boolean');if(null===$(/^%?[^%]*%?$/,e))throw new u("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=W(e),o=r.length>0?r[0]:"",n=K("%"+o+"%",t),i=n.name,a=n.value,s=!1,l=n.alias;l&&(o=l[0],U(r,q([0,1],l)));for(var d=1,h=!0;d<r.length;d+=1){var p=r[d],m=H(p,0,1),f=H(p,-1);if(('"'===m||"'"===m||"`"===m||'"'===f||"'"===f||"`"===f)&&m!==f)throw new u("property names with quotes must have matching quotes");if("constructor"!==p&&h||(s=!0),o+="."+p,F(j,i="%"+o+"%"))a=j[i];else if(null!=a){if(!(p in a)){if(!t)throw new c("base intrinsic for "+e+" exists, but the property is not available.");return}if(_&&d+1>=r.length){var y=_(a,p);a=(h=!!y)&&"get"in y&&!("originalValue"in y.get)?y.get:a[p]}else h=F(a,p),a=a[p];h&&!s&&(j[i]=a)}}return a}},85681:(e,t,r)=>{"use strict";var o=r(44640),n=Object.prototype.hasOwnProperty,i=Array.isArray,a=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),s=function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(i(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}},l=function(e,t){for(var r=t&&t.plainObjects?{__proto__:null}:{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r};e.exports={arrayToObject:l,assign:function(e,t){return Object.keys(t).reduce(function(e,r){return e[r]=t[r],e},e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],o=0;o<t.length;++o)for(var n=t[o],i=n.obj[n.prop],a=Object.keys(i),l=0;l<a.length;++l){var u=a[l],c=i[u];"object"==typeof c&&null!==c&&-1===r.indexOf(c)&&(t.push({obj:i,prop:u}),r.push(c))}return s(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},encode:function(e,t,r,n,i){if(0===e.length)return e;var s=e;if("symbol"==typeof e?s=Symbol.prototype.toString.call(e):"string"!=typeof e&&(s=String(e)),"iso-8859-1"===r)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});for(var l="",u=0;u<s.length;u+=1024){for(var c=s.length>=1024?s.slice(u,u+1024):s,d=[],h=0;h<c.length;++h){var p=c.charCodeAt(h);if(45===p||46===p||95===p||126===p||p>=48&&p<=57||p>=65&&p<=90||p>=97&&p<=122||i===o.RFC1738&&(40===p||41===p)){d[d.length]=c.charAt(h);continue}if(p<128){d[d.length]=a[p];continue}if(p<2048){d[d.length]=a[192|p>>6]+a[128|63&p];continue}if(p<55296||p>=57344){d[d.length]=a[224|p>>12]+a[128|p>>6&63]+a[128|63&p];continue}h+=1,p=65536+((1023&p)<<10|1023&c.charCodeAt(h)),d[d.length]=a[240|p>>18]+a[128|p>>12&63]+a[128|p>>6&63]+a[128|63&p]}l+=d.join("")}return l},isBuffer:function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(i(e)){for(var r=[],o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r&&"function"!=typeof r){if(i(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!n.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var a=t;return(i(t)&&!i(r)&&(a=l(t,o)),i(t)&&i(r))?(r.forEach(function(r,i){if(n.call(t,i)){var a=t[i];a&&"object"==typeof a&&r&&"object"==typeof r?t[i]=e(a,r,o):t.push(r)}else t[i]=r}),t):Object.keys(r).reduce(function(t,i){var a=r[i];return n.call(t,i)?t[i]=e(t[i],a,o):t[i]=a,t},a)}}},85903:(e,t,r)=>{"use strict";var o=r(85681),n=Object.prototype.hasOwnProperty,i=Array.isArray,a={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:o.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},s=function(e,t,r){if(e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1)return e.split(",");if(t.throwOnLimitExceeded&&r>=t.arrayLimit)throw RangeError("Array limit exceeded. Only "+t.arrayLimit+" element"+(1===t.arrayLimit?"":"s")+" allowed in an array.");return e},l=function(e,t){var r={__proto__:null},l=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;l=l.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var u=t.parameterLimit===1/0?void 0:t.parameterLimit,c=l.split(t.delimiter,t.throwOnLimitExceeded?u+1:u);if(t.throwOnLimitExceeded&&c.length>u)throw RangeError("Parameter limit exceeded. Only "+u+" parameter"+(1===u?"":"s")+" allowed.");var d=-1,h=t.charset;if(t.charsetSentinel)for(p=0;p<c.length;++p)0===c[p].indexOf("utf8=")&&("utf8=%E2%9C%93"===c[p]?h="utf-8":"utf8=%26%2310003%3B"===c[p]&&(h="iso-8859-1"),d=p,p=c.length);for(p=0;p<c.length;++p)if(p!==d){var p,m,f,y=c[p],v=y.indexOf("]="),P=-1===v?y.indexOf("="):v+1;-1===P?(m=t.decoder(y,a.decoder,h,"key"),f=t.strictNullHandling?null:""):(m=t.decoder(y.slice(0,P),a.decoder,h,"key"),f=o.maybeMap(s(y.slice(P+1),t,i(r[m])?r[m].length:0),function(e){return t.decoder(e,a.decoder,h,"value")})),f&&t.interpretNumericEntities&&"iso-8859-1"===h&&(f=String(f).replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),y.indexOf("[]=")>-1&&(f=i(f)?[f]:f);var g=n.call(r,m);g&&"combine"===t.duplicates?r[m]=o.combine(r[m],f):g&&"last"!==t.duplicates||(r[m]=f)}return r},u=function(e,t,r,n){var i=0;if(e.length>0&&"[]"===e[e.length-1]){var a=e.slice(0,-1).join("");i=Array.isArray(t)&&t[a]?t[a].length:0}for(var l=n?t:s(t,r,i),u=e.length-1;u>=0;--u){var c,d=e[u];if("[]"===d&&r.parseArrays)c=r.allowEmptyArrays&&(""===l||r.strictNullHandling&&null===l)?[]:o.combine([],l);else{c=r.plainObjects?{__proto__:null}:{};var h="["===d.charAt(0)&&"]"===d.charAt(d.length-1)?d.slice(1,-1):d,p=r.decodeDotInKeys?h.replace(/%2E/g,"."):h,m=parseInt(p,10);r.parseArrays||""!==p?!isNaN(m)&&d!==p&&String(m)===p&&m>=0&&r.parseArrays&&m<=r.arrayLimit?(c=[])[m]=l:"__proto__"!==p&&(c[p]=l):c={0:l}}l=c}return l},c=function(e,t,r,o){if(e){var i=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,a=/(\[[^[\]]*])/g,s=r.depth>0&&/(\[[^[\]]*])/.exec(i),l=s?i.slice(0,s.index):i,c=[];if(l){if(!r.plainObjects&&n.call(Object.prototype,l)&&!r.allowPrototypes)return;c.push(l)}for(var d=0;r.depth>0&&null!==(s=a.exec(i))&&d<r.depth;){if(d+=1,!r.plainObjects&&n.call(Object.prototype,s[1].slice(1,-1))&&!r.allowPrototypes)return;c.push(s[1])}if(s){if(!0===r.strictDepth)throw RangeError("Input depth exceeded depth option of "+r.depth+" and strictDepth is true");c.push("["+i.slice(s.index)+"]")}return u(c,t,r,o)}},d=function(e){if(!e)return a;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(void 0!==e.throwOnLimitExceeded&&"boolean"!=typeof e.throwOnLimitExceeded)throw TypeError("`throwOnLimitExceeded` option must be a boolean");var t=void 0===e.charset?a.charset:e.charset,r=void 0===e.duplicates?a.duplicates:e.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||a.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:a.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:a.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:a.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:a.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:a.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:a.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:a.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:a.decoder,delimiter:"string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e.delimiter:a.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:a.depth,duplicates:r,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:a.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:a.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:a.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:a.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:a.strictNullHandling,throwOnLimitExceeded:"boolean"==typeof e.throwOnLimitExceeded&&e.throwOnLimitExceeded}};e.exports=function(e,t){var r=d(t);if(""===e||null==e)return r.plainObjects?{__proto__:null}:{};for(var n="string"==typeof e?l(e,r):e,i=r.plainObjects?{__proto__:null}:{},a=Object.keys(n),s=0;s<a.length;++s){var u=a[s],h=c(u,n[u],r,"string"==typeof e);i=o.merge(i,h,r)}return!0===r.allowSparse?i:o.compact(i)}},87897:(e,t,r)=>{"use strict";var o=r(88486),n=r(19118),i=r(90428),a=r(50122),s=r(42690)||a||i;e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new o("Side channel does not contain "+n(e))},delete:function(t){return!!e&&e.delete(t)},get:function(t){return e&&e.get(t)},has:function(t){return!!e&&e.has(t)},set:function(t,r){e||(e=s()),e.set(t,r)}};return t}},88486:e=>{"use strict";e.exports=TypeError},90428:(e,t,r)=>{"use strict";var o=r(19118),n=r(88486),i=function(e,t,r){for(var o,n=e;null!=(o=n.next);n=o)if(o.key===t)return n.next=o.next,r||(o.next=e.next,e.next=o),o},a=function(e,t){if(e){var r=i(e,t);return r&&r.value}},s=function(e,t,r){var o=i(e,t);o?o.value=r:e.next={key:t,next:e.next,value:r}},l=function(e,t){if(e)return i(e,t,!0)};e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new n("Side channel does not contain "+o(e))},delete:function(t){var r=e&&e.next,o=l(e,t);return o&&r&&r===o&&(e=void 0),!!o},get:function(t){return a(e,t)},has:function(t){var r;return!!(r=e)&&!!i(r,t)},set:function(t,r){e||(e={next:void 0}),s(e,t,r)}};return t}},92590:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t||"[object Symbol]"!==Object.prototype.toString.call(t)||"[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(var o in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0}},93377:e=>{"use strict";e.exports=Function.prototype.apply},93926:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},96961:e=>{"use strict";e.exports=Math.min},97877:(e,t,r)=>{"use strict";r.d(t,{A:()=>nL});var o={};r.r(o),r.d(o,{StripeAPIError:()=>K,StripeAuthenticationError:()=>V,StripeCardError:()=>B,StripeConnectionError:()=>X,StripeError:()=>z,StripeIdempotencyError:()=>Z,StripeInvalidGrantError:()=>ee,StripeInvalidRequestError:()=>W,StripePermissionError:()=>J,StripeRateLimitError:()=>Q,StripeSignatureVerificationError:()=>Y,StripeUnknownError:()=>et,TemporarySessionExpiredError:()=>er,generateV1Error:()=>H,generateV2Error:()=>$});var n={};r.r(n),r.d(n,{Account:()=>rF,AccountLinks:()=>rU,AccountSessions:()=>rH,Accounts:()=>rF,ApplePayDomains:()=>rz,ApplicationFees:()=>rW,Apps:()=>nv,Balance:()=>rV,BalanceTransactions:()=>rQ,Billing:()=>nP,BillingPortal:()=>ng,Charges:()=>rY,Checkout:()=>nT,Climate:()=>n_,ConfirmationTokens:()=>r1,CountrySpecs:()=>r2,Coupons:()=>r4,CreditNotes:()=>r6,CustomerSessions:()=>r9,Customers:()=>oe,Disputes:()=>or,Entitlements:()=>nE,EphemeralKeys:()=>on,Events:()=>oa,ExchangeRates:()=>ol,FileLinks:()=>oc,Files:()=>op,FinancialConnections:()=>nS,Forwarding:()=>nb,Identity:()=>nO,InvoiceItems:()=>of,InvoicePayments:()=>ov,InvoiceRenderingTemplates:()=>og,Invoices:()=>o_,Issuing:()=>nx,Mandates:()=>oS,OAuth:()=>ox,PaymentIntents:()=>oA,PaymentLinks:()=>oC,PaymentMethodConfigurations:()=>oI,PaymentMethodDomains:()=>ok,PaymentMethods:()=>oN,Payouts:()=>oF,Plans:()=>oU,Prices:()=>oH,Products:()=>oz,PromotionCodes:()=>oW,Quotes:()=>oV,Radar:()=>nw,Refunds:()=>oQ,Reporting:()=>nA,Reviews:()=>oY,SetupAttempts:()=>o1,SetupIntents:()=>o2,ShippingRates:()=>o4,Sigma:()=>nR,Sources:()=>o6,SubscriptionItems:()=>o9,SubscriptionSchedules:()=>ne,Subscriptions:()=>nr,Tax:()=>nC,TaxCodes:()=>nn,TaxIds:()=>na,TaxRates:()=>nl,Terminal:()=>nG,TestHelpers:()=>nI,Tokens:()=>nc,Topups:()=>nh,Transfers:()=>nm,Treasury:()=>nj,V2:()=>nk,WebhookEndpoints:()=>ny});var i=r(55511),a=r(94735);class s{computeHMACSignature(e,t){throw Error("computeHMACSignature not implemented.")}computeHMACSignatureAsync(e,t){throw Error("computeHMACSignatureAsync not implemented.")}computeSHA256Async(e){throw Error("computeSHA256 not implemented.")}}class l extends Error{}class u extends s{computeHMACSignature(e,t){return i.createHmac("sha256",t).update(e,"utf8").digest("hex")}async computeHMACSignatureAsync(e,t){return await this.computeHMACSignature(e,t)}async computeSHA256Async(e){return new Uint8Array(await i.createHash("sha256").update(e).digest())}}var c=r(81630),d=r.t(c,2),h=r(55591),p=r.t(h,2);class m{getClientName(){throw Error("getClientName not implemented.")}makeRequest(e,t,r,o,n,i,a,s){throw Error("makeRequest not implemented.")}static makeTimeoutError(){let e=TypeError(m.TIMEOUT_ERROR_CODE);return e.code=m.TIMEOUT_ERROR_CODE,e}}m.CONNECTION_CLOSED_ERROR_CODES=["ECONNRESET","EPIPE"],m.TIMEOUT_ERROR_CODE="ETIMEDOUT";class f{constructor(e,t){this._statusCode=e,this._headers=t}getStatusCode(){return this._statusCode}getHeaders(){return this._headers}getRawResponse(){throw Error("getRawResponse not implemented.")}toStream(e){throw Error("toStream not implemented.")}toJSON(){throw Error("toJSON not implemented.")}}let y=c||d,v=h||p,P=new y.Agent({keepAlive:!0}),g=new v.Agent({keepAlive:!0});class T extends m{constructor(e){super(),this._agent=e}getClientName(){return"node"}makeRequest(e,t,r,o,n,i,a,s){let l="http"===a,u=this._agent;return u||(u=l?P:g),new Promise((a,c)=>{let d=(l?y:v).request({host:e,port:t,path:r,method:o,agent:u,headers:n,ciphers:"DEFAULT:!aNULL:!eNULL:!LOW:!EXPORT:!SSLv2:!MD5"});d.setTimeout(s,()=>{d.destroy(m.makeTimeoutError())}),d.on("response",e=>{a(new _(e))}),d.on("error",e=>{c(e)}),d.once("socket",e=>{e.connecting?e.once(l?"connect":"secureConnect",()=>{d.write(i),d.end()}):(d.write(i),d.end())})})}}class _ extends f{constructor(e){super(e.statusCode,e.headers||{}),this._res=e}getRawResponse(){return this._res}toStream(e){return this._res.once("end",()=>e()),this._res}toJSON(){return new Promise((e,t)=>{let r="";this._res.setEncoding("utf8"),this._res.on("data",e=>{r+=e}),this._res.once("end",()=>{try{e(JSON.parse(r))}catch(e){t(e)}})})}}var E=r(33376);let S=["apiKey","idempotencyKey","stripeAccount","apiVersion","maxNetworkRetries","timeout","host","authenticator","stripeContext","additionalHeaders","streaming"];function b(e){return e&&"object"==typeof e&&S.some(t=>Object.prototype.hasOwnProperty.call(e,t))}function O(e,t){return E.stringify(e,{serializeDate:e=>Math.floor(e.getTime()/1e3).toString(),arrayFormat:"v2"==t?"repeat":"indices"}).replace(/%5B/g,"[").replace(/%5D/g,"]")}let x=(()=>{let e={"\n":"\\n",'"':'\\"',"\u2028":"\\u2028","\u2029":"\\u2029"};return t=>{let r=t.replace(/["\n\r\u2028\u2029]/g,t=>e[t]);return e=>r.replace(/\{([\s\S]+?)\}/g,(t,r)=>{let o=e[r];return["number","string","boolean"].includes(typeof o)?encodeURIComponent(o):""})}})();function w(e){if(!Array.isArray(e)||!e[0]||"object"!=typeof e[0])return{};if(!b(e[0]))return e.shift();let t=Object.keys(e[0]),r=t.filter(e=>S.includes(e));return r.length>0&&r.length!==t.length&&G(`Options found in arguments (${r.join(", ")}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options.`),{}}function A(e){let t={host:null,headers:{},settings:{},streaming:!1};if(e.length>0){let r=e[e.length-1];if("string"==typeof r)t.authenticator=j(e.pop());else if(b(r)){let r=Object.assign({},e.pop()),o=Object.keys(r).filter(e=>!S.includes(e));if(o.length&&G(`Invalid options found (${o.join(", ")}); ignoring.`),r.apiKey&&(t.authenticator=j(r.apiKey)),r.idempotencyKey&&(t.headers["Idempotency-Key"]=r.idempotencyKey),r.stripeAccount&&(t.headers["Stripe-Account"]=r.stripeAccount),r.stripeContext){if(t.headers["Stripe-Account"])throw Error("Can't specify both stripeAccount and stripeContext.");t.headers["Stripe-Context"]=r.stripeContext}if(r.apiVersion&&(t.headers["Stripe-Version"]=r.apiVersion),Number.isInteger(r.maxNetworkRetries)&&(t.settings.maxNetworkRetries=r.maxNetworkRetries),Number.isInteger(r.timeout)&&(t.settings.timeout=r.timeout),r.host&&(t.host=r.host),r.authenticator){if(r.apiKey)throw Error("Can't specify both apiKey and authenticator.");if("function"!=typeof r.authenticator)throw Error("The authenticator must be a function receiving a request as the first parameter.");t.authenticator=r.authenticator}r.additionalHeaders&&(t.headers=r.additionalHeaders),r.streaming&&(t.streaming=!0)}}return t}function R(e){if("object"!=typeof e)throw Error("Argument must be an object");return Object.keys(e).reduce((t,r)=>(null!=e[r]&&(t[r]=e[r]),t),{})}function C(e,t){return t?e.then(e=>{setTimeout(()=>{t(null,e)},0)},e=>{setTimeout(()=>{t(e,null)},0)}):e}function G(e){return"function"!=typeof process.emitWarning?console.warn(`Stripe: ${e}`):process.emitWarning(e,"Stripe")}function I(e,t,r){if(!Number.isInteger(t))if(void 0!==r)return r;else throw Error(`${e} must be an integer`);return t}function j(e){let t=t=>(t.headers.Authorization="Bearer "+e,Promise.resolve());return t._apiKey=e,t}function k(e,t){return this[e]instanceof Date?Math.floor(this[e].getTime()/1e3).toString():t}function D(e){return e&&e.startsWith("/v2")?"v2":"v1"}function N(e){return Array.isArray(e)?e.join(", "):String(e)}class M extends m{constructor(e){if(super(),!e){if(!globalThis.fetch)throw Error("fetch() function not provided and is not defined in the global scope. You must provide a fetch implementation.");e=globalThis.fetch}globalThis.AbortController?this._fetchFn=M.makeFetchWithAbortTimeout(e):this._fetchFn=M.makeFetchWithRaceTimeout(e)}static makeFetchWithRaceTimeout(e){return(t,r,o)=>{let n,i=new Promise((e,t)=>{n=setTimeout(()=>{n=null,t(m.makeTimeoutError())},o)});return Promise.race([e(t,r),i]).finally(()=>{n&&clearTimeout(n)})}}static makeFetchWithAbortTimeout(e){return async(t,r,o)=>{let n=new AbortController,i=setTimeout(()=>{i=null,n.abort(m.makeTimeoutError())},o);try{return await e(t,Object.assign(Object.assign({},r),{signal:n.signal}))}catch(e){if("AbortError"===e.name)throw m.makeTimeoutError();throw e}finally{i&&clearTimeout(i)}}}getClientName(){return"fetch"}async makeRequest(e,t,r,o,n,i,a,s){let l=new URL(r,`${"http"===a?"http":"https"}://${e}`);l.port=t;let u="POST"==o||"PUT"==o||"PATCH"==o,c=i||(u?"":void 0);return new F(await this._fetchFn(l.toString(),{method:o,headers:Object.entries(n).map(([e,t])=>[e,N(t)]),body:"object"==typeof c?JSON.stringify(c):c},s))}}class F extends f{constructor(e){super(e.status,F._transformHeadersToObject(e.headers)),this._res=e}getRawResponse(){return this._res}toStream(e){return e(),this._res.body}toJSON(){return this._res.json()}static _transformHeadersToObject(e){let t={};for(let r of e){if(!Array.isArray(r)||2!=r.length)throw Error("Response objects produced by the fetch function given to FetchHttpClient do not have an iterable headers map. Response#headers should be an iterable object.");t[r[0]]=r[1]}return t}}class q extends s{constructor(e){super(),this.subtleCrypto=e||crypto.subtle}computeHMACSignature(e,t){throw new l("SubtleCryptoProvider cannot be used in a synchronous context.")}async computeHMACSignatureAsync(e,t){let r=new TextEncoder,o=await this.subtleCrypto.importKey("raw",r.encode(t),{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),n=new Uint8Array(await this.subtleCrypto.sign("hmac",o,r.encode(e))),i=Array(n.length);for(let e=0;e<n.length;e++)i[e]=U[n[e]];return i.join("")}async computeSHA256Async(e){return new Uint8Array(await this.subtleCrypto.digest("SHA-256",e))}}let U=Array(256);for(let e=0;e<U.length;e++)U[e]=e.toString(16).padStart(2,"0");class L{constructor(){this._fetchFn=null,this._agent=null}getUname(){throw Error("getUname not implemented.")}uuid4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}secureCompare(e,t){if(e.length!==t.length)return!1;let r=e.length,o=0;for(let n=0;n<r;++n)o|=e.charCodeAt(n)^t.charCodeAt(n);return 0===o}createEmitter(){throw Error("createEmitter not implemented.")}tryBufferData(e){throw Error("tryBufferData not implemented.")}createNodeHttpClient(e){throw Error("createNodeHttpClient not implemented.")}createFetchHttpClient(e){return new M(e)}createDefaultHttpClient(){throw Error("createDefaultHttpClient not implemented.")}createNodeCryptoProvider(){throw Error("createNodeCryptoProvider not implemented.")}createSubtleCryptoProvider(e){return new q(e)}createDefaultCryptoProvider(){throw Error("createDefaultCryptoProvider not implemented.")}}let H=e=>{switch(e.type){case"card_error":return new B(e);case"invalid_request_error":return new W(e);case"api_error":return new K(e);case"authentication_error":return new V(e);case"rate_limit_error":return new Q(e);case"idempotency_error":return new Z(e);case"invalid_grant":return new ee(e);default:return new et(e)}},$=e=>"temporary_session_expired"===e.type?new er(e):"invalid_fields"===e.code?new W(e):H(e);class z extends Error{constructor(e={},t=null){var r;super(e.message),this.type=t||this.constructor.name,this.raw=e,this.rawType=e.type,this.code=e.code,this.doc_url=e.doc_url,this.param=e.param,this.detail=e.detail,this.headers=e.headers,this.requestId=e.requestId,this.statusCode=e.statusCode,this.message=null!=(r=e.message)?r:"",this.userMessage=e.user_message,this.charge=e.charge,this.decline_code=e.decline_code,this.payment_intent=e.payment_intent,this.payment_method=e.payment_method,this.payment_method_type=e.payment_method_type,this.setup_intent=e.setup_intent,this.source=e.source}}z.generate=H;class B extends z{constructor(e={}){super(e,"StripeCardError")}}class W extends z{constructor(e={}){super(e,"StripeInvalidRequestError")}}class K extends z{constructor(e={}){super(e,"StripeAPIError")}}class V extends z{constructor(e={}){super(e,"StripeAuthenticationError")}}class J extends z{constructor(e={}){super(e,"StripePermissionError")}}class Q extends z{constructor(e={}){super(e,"StripeRateLimitError")}}class X extends z{constructor(e={}){super(e,"StripeConnectionError")}}class Y extends z{constructor(e,t,r={}){super(r,"StripeSignatureVerificationError"),this.header=e,this.payload=t}}class Z extends z{constructor(e={}){super(e,"StripeIdempotencyError")}}class ee extends z{constructor(e={}){super(e,"StripeInvalidGrantError")}}class et extends z{constructor(e={}){super(e,"StripeUnknownError")}}class er extends z{constructor(e={}){super(e,"TemporarySessionExpiredError")}}var eo=r(79646);class en extends z{}class ei extends L{constructor(){super(),this._exec=eo.exec,this._UNAME_CACHE=null}uuid4(){return i.randomUUID?i.randomUUID():super.uuid4()}getUname(){return this._UNAME_CACHE||(this._UNAME_CACHE=new Promise((e,t)=>{try{this._exec("uname -a",(t,r)=>{if(t)return e(null);e(r)})}catch(t){e(null)}})),this._UNAME_CACHE}secureCompare(e,t){if(!e||!t)throw Error("secureCompare must receive two arguments");if(e.length!==t.length)return!1;if(i.timingSafeEqual){let r=new TextEncoder,o=r.encode(e),n=r.encode(t);return i.timingSafeEqual(o,n)}return super.secureCompare(e,t)}createEmitter(){return new a.EventEmitter}tryBufferData(e){if(!(e.file.data instanceof a.EventEmitter))return Promise.resolve(e);let t=[];return new Promise((r,o)=>{e.file.data.on("data",e=>{t.push(e)}).once("end",()=>{let o=Object.assign({},e);o.file.data=function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),r=0;return e.forEach(e=>{t.set(e,r),r+=e.length}),t}(t),r(o)}).on("error",e=>{o(new en({message:"An error occurred while attempting to process the file for upload.",detail:e}))})})}createNodeHttpClient(e){return new T(e)}createDefaultHttpClient(){return new T}createNodeCryptoProvider(){return new u}createDefaultCryptoProvider(){return this.createNodeCryptoProvider()}}class ea{constructor(e,t){this._stripe=e,this._maxBufferedRequestMetric=t}_addHeadersDirectlyToObject(e,t){e.requestId=t["request-id"],e.stripeAccount=e.stripeAccount||t["stripe-account"],e.apiVersion=e.apiVersion||t["stripe-version"],e.idempotencyKey=e.idempotencyKey||t["idempotency-key"]}_makeResponseEvent(e,t,r){let o=Date.now(),n=o-e.request_start_time;return R({api_version:r["stripe-version"],account:r["stripe-account"],idempotency_key:r["idempotency-key"],method:e.method,path:e.path,status:t,request_id:this._getRequestId(r),elapsed:n,request_start_time:e.request_start_time,request_end_time:o})}_getRequestId(e){return e["request-id"]}_streamingResponseHandler(e,t,r){return o=>{let n=o.getHeaders(),i=o.toStream(()=>{let r=this._makeResponseEvent(e,o.getStatusCode(),n);this._stripe._emitter.emit("response",r),this._recordRequestMetrics(this._getRequestId(n),r.elapsed,t)});return this._addHeadersDirectlyToObject(i,n),r(null,i)}}_jsonResponseHandler(e,t,r,o){return n=>{let i=n.getHeaders(),a=this._getRequestId(i),s=n.getStatusCode(),l=this._makeResponseEvent(e,s,i);this._stripe._emitter.emit("response",l),n.toJSON().then(e=>{if(e.error){let r;throw"string"==typeof e.error&&(e.error={type:e.error,message:e.error_description}),e.error.headers=i,e.error.statusCode=s,e.error.requestId=a,401===s?new V(e.error):403===s?new J(e.error):429===s?new Q(e.error):"v2"===t?$(e.error):H(e.error)}return e},e=>{throw new K({message:"Invalid JSON received from the Stripe API",exception:e,requestId:i["request-id"]})}).then(e=>{this._recordRequestMetrics(a,l.elapsed,r);let t=n.getRawResponse();this._addHeadersDirectlyToObject(t,i),Object.defineProperty(e,"lastResponse",{enumerable:!1,writable:!1,value:t}),o(null,e)},e=>o(e,null))}}static _generateConnectionErrorMessage(e){return`An error occurred with our connection to Stripe.${e>0?` Request was retried ${e} times.`:""}`}static _shouldRetry(e,t,r,o){return!!(o&&0===t&&m.CONNECTION_CLOSED_ERROR_CODES.includes(o.code))||!(t>=r)&&(!e||"false"!==e.getHeaders()["stripe-should-retry"]&&!!("true"===e.getHeaders()["stripe-should-retry"]||409===e.getStatusCode()||e.getStatusCode()>=500))}_getSleepTimeInMS(e,t=null){let r=this._stripe.getInitialNetworkRetryDelay(),o=Math.min(r*Math.pow(2,e-1),this._stripe.getMaxNetworkRetryDelay());return o*=.5*(1+Math.random()),o=Math.max(r,o),Number.isInteger(t)&&t<=60&&(o=Math.max(o,t)),1e3*o}_getMaxNetworkRetries(e={}){return void 0!==e.maxNetworkRetries&&Number.isInteger(e.maxNetworkRetries)?e.maxNetworkRetries:this._stripe.getMaxNetworkRetries()}_defaultIdempotencyKey(e,t,r){let o=this._getMaxNetworkRetries(t),n=()=>`stripe-node-retry-${this._stripe._platformFunctions.uuid4()}`;if("v2"===r){if("POST"===e||"DELETE"===e)return n()}else if("v1"===r&&"POST"===e&&o>0)return n();return null}_makeHeaders({contentType:e,contentLength:t,apiVersion:r,clientUserAgent:o,method:n,userSuppliedHeaders:i,userSuppliedSettings:a,stripeAccount:s,stripeContext:l,apiMode:u}){let c={Accept:"application/json","Content-Type":e,"User-Agent":this._getUserAgentString(u),"X-Stripe-Client-User-Agent":o,"X-Stripe-Client-Telemetry":this._getTelemetryHeader(),"Stripe-Version":r,"Stripe-Account":s,"Stripe-Context":l,"Idempotency-Key":this._defaultIdempotencyKey(n,a,u)},d="POST"==n||"PUT"==n||"PATCH"==n;return(d||t)&&(d||G(`${n} method had non-zero contentLength but no payload is expected for this verb`),c["Content-Length"]=t),Object.assign(R(c),i&&"object"==typeof i?Object.keys(i).reduce((e,t)=>(e[t.split("-").map(e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()).join("-")]=i[t],e),{}):i)}_getUserAgentString(e){let t=this._stripe.getConstant("PACKAGE_VERSION"),r=this._stripe._appInfo?this._stripe.getAppInfoAsString():"";return`Stripe/${e} NodeBindings/${t} ${r}`.trim()}_getTelemetryHeader(){if(this._stripe.getTelemetryEnabled()&&this._stripe._prevRequestMetrics.length>0)return JSON.stringify({last_request_metrics:this._stripe._prevRequestMetrics.shift()})}_recordRequestMetrics(e,t,r){if(this._stripe.getTelemetryEnabled()&&e)if(this._stripe._prevRequestMetrics.length>this._maxBufferedRequestMetric)G("Request metrics buffer is full, dropping telemetry message.");else{let o={request_id:e,request_duration_ms:t};r&&r.length>0&&(o.usage=r),this._stripe._prevRequestMetrics.push(o)}}_rawRequest(e,t,r,o){return new Promise((n,i)=>{let a;try{let n=e.toUpperCase();if("POST"!==n&&r&&0!==Object.keys(r).length)throw Error("rawRequest only supports params on POST requests. Please pass null and add your parameters to path.");let i=[].slice.call([r,o]),s=w(i),l="POST"===n?Object.assign({},s):null,u=A(i),c=u.headers,d=u.authenticator;a={requestMethod:n,requestPath:t,bodyData:l,queryData:{},authenticator:d,headers:c,host:u.host,streaming:!!u.streaming,settings:{},usage:["raw_request"]}}catch(e){i(e);return}let{headers:s,settings:l}=a,u=a.authenticator;this._request(a.requestMethod,a.host,t,a.bodyData,u,{headers:s,settings:l,streaming:a.streaming},a.usage,function(e,t){e?i(e):n(t)})})}_request(e,t,r,o,n,i,a=[],s,l=null){var u;let c;n=null!=(u=null!=n?n:this._stripe._authenticator)?u:null;let d=D(r),h=(e,t,r,o,n)=>setTimeout(e,this._getSleepTimeInMS(o,n),t,r,o+1),p=(o,l,u)=>{let f=i.settings&&i.settings.timeout&&Number.isInteger(i.settings.timeout)&&i.settings.timeout>=0?i.settings.timeout:this._stripe.getApiField("timeout"),y={host:t||this._stripe.getApiField("host"),port:this._stripe.getApiField("port"),path:r,method:e,headers:Object.assign({},l),body:c,protocol:this._stripe.getApiField("protocol")};n(y).then(()=>{let t=this._stripe.getApiField("httpClient").makeRequest(y.host,y.port,y.path,y.method,y.headers,y.body,y.protocol,f),n=Date.now(),c=R({api_version:o,account:N(l["Stripe-Account"]),idempotency_key:N(l["Idempotency-Key"]),method:e,path:r,request_start_time:n}),v=u||0,P=this._getMaxNetworkRetries(i.settings||{});this._stripe._emitter.emit("request",c),t.then(e=>{if(ea._shouldRetry(e,v,P)){var t;return h(p,o,l,v,Number(Array.isArray(t=e.getHeaders()["retry-after"])?t[0]:t))}return i.streaming&&400>e.getStatusCode()?this._streamingResponseHandler(c,a,s)(e):this._jsonResponseHandler(c,d,a,s)(e)}).catch(e=>ea._shouldRetry(null,v,P,e)?h(p,o,l,v,null):s(new X({message:e.code&&e.code===m.TIMEOUT_ERROR_CODE?`Request aborted due to timeout being reached (${f}ms)`:ea._generateConnectionErrorMessage(v),detail:e})))}).catch(e=>{throw new z({message:"Unable to authenticate the request",exception:e})})},f=(t,r)=>{if(t)return s(t);c=r,this._stripe.getClientUserAgent(t=>{let r=this._stripe.getApiField("version"),o=this._makeHeaders({contentType:"v2"==d?"application/json":"application/x-www-form-urlencoded",contentLength:c.length,apiVersion:r,clientUserAgent:t,method:e,userSuppliedHeaders:i.headers,userSuppliedSettings:i.settings,stripeAccount:"v2"==d?null:this._stripe.getApiField("stripeAccount"),stripeContext:"v2"==d?this._stripe.getApiField("stripeContext"):null,apiMode:d});p(r,o,0)})};if(l)l(e,o,i.headers,f);else{let e;f(null,e="v2"==d?o?JSON.stringify(o,k):"":O(o||{},d))}}}class es{constructor(e,t,r,o){this.index=0,this.pagePromise=e,this.promiseCache={currentPromise:null},this.requestArgs=t,this.spec=r,this.stripeResource=o}async iterate(e){if(!(e&&e.data&&"number"==typeof e.data.length))throw Error("Unexpected: Stripe API response does not have a well-formed `data` array.");let t=ep(this.requestArgs);if(this.index<e.data.length){let r=t?e.data.length-1-this.index:this.index,o=e.data[r];return this.index+=1,{value:o,done:!1}}if(e.has_more){this.index=0,this.pagePromise=this.getNextPage(e);let t=await this.pagePromise;return this.iterate(t)}return{done:!0,value:void 0}}getNextPage(e){throw Error("Unimplemented")}async _next(){return this.iterate(await this.pagePromise)}next(){if(this.promiseCache.currentPromise)return this.promiseCache.currentPromise;let e=(async()=>{let e=await this._next();return this.promiseCache.currentPromise=null,e})();return this.promiseCache.currentPromise=e,e}}class el extends es{getNextPage(e){let t=ep(this.requestArgs),r=function(e,t){let r=t?0:e.data.length-1,o=e.data[r],n=o&&o.id;if(!n)throw Error("Unexpected: No `id` found on the last item while auto-paging a list.");return n}(e,t);return this.stripeResource._makeRequest(this.requestArgs,this.spec,{[t?"ending_before":"starting_after"]:r})}}class eu extends es{getNextPage(e){if(!e.next_page)throw Error("Unexpected: Stripe API response does not have a well-formed `next_page` field, but `has_more` was true.");return this.stripeResource._makeRequest(this.requestArgs,this.spec,{page:e.next_page})}}class ec{constructor(e,t,r,o){this.currentPageIterator=(async()=>(await e).data[Symbol.iterator]())(),this.nextPageUrl=(async()=>(await e).next_page_url||null)(),this.requestArgs=t,this.spec=r,this.stripeResource=o}async turnPage(){let e=await this.nextPageUrl;if(!e)return null;this.spec.fullPath=e;let t=await this.stripeResource._makeRequest([],this.spec,{});return this.nextPageUrl=Promise.resolve(t.next_page_url),this.currentPageIterator=Promise.resolve(t.data[Symbol.iterator]()),this.currentPageIterator}async next(){{let e=(await this.currentPageIterator).next();if(!e.done)return{done:!1,value:e.value}}let e=await this.turnPage();if(!e)return{done:!0,value:void 0};let t=e.next();return t.done?{done:!0,value:void 0}:{done:!1,value:t.value}}}let ed=(e,t,r,o)=>{let n=D(r.fullPath||r.path);return"v2"!==n&&"search"===r.methodType?eh(new eu(o,t,r,e)):"v2"!==n&&"list"===r.methodType?eh(new el(o,t,r,e)):"v2"===n&&"list"===r.methodType?eh(new ec(o,t,r,e)):null},eh=e=>{var t,r;let o=(t=(...t)=>e.next(...t),function(){var e,r;let o=[].slice.call(arguments),n=function(e){if(0===e.length)return;let t=e[0];if("function"!=typeof t)throw Error(`The first argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);if(2===t.length)return t;if(t.length>2)throw Error(`The \`onItem\` callback function passed to autoPagingEach must accept at most two arguments; got ${t}`);return function(e,r){r(t(e))}}(o),i=function(e){if(e.length<2)return null;let t=e[1];if("function"!=typeof t)throw Error(`The second argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);return t}(o);if(o.length>2)throw Error(`autoPagingEach takes up to two arguments; received ${o}`);return C((e=t,r=n,new Promise((t,o)=>{e().then(function o(n){if(n.done)return void t();let i=n.value;return new Promise(e=>{r(i,e)}).then(t=>!1===t?o({done:!0,value:void 0}):e().then(o))}).catch(o)})),i)}),n=(r=o,function(e,t){let o=e&&e.limit;if(!o)throw Error("You must pass a `limit` option to autoPagingToArray, e.g., `autoPagingToArray({limit: 1000});`.");if(o>1e4)throw Error("You cannot specify a limit of more than 10,000 items to fetch in `autoPagingToArray`; use `autoPagingEach` to iterate through longer lists.");return C(new Promise((e,t)=>{let n=[];r(e=>{if(n.push(e),n.length>=o)return!1}).then(()=>{e(n)}).catch(t)}),t)}),i={autoPagingEach:o,autoPagingToArray:n,next:()=>e.next(),return:()=>({}),["undefined"!=typeof Symbol&&Symbol.asyncIterator?Symbol.asyncIterator:"@@asyncIterator"]:()=>i};return i};function ep(e){return!!w([].slice.call(e)).ending_before}function em(e,t){if(this._stripe=e,t)throw Error("Support for curried url params was dropped in stripe-node v7.0.0. Instead, pass two ids.");this.basePath=x(this.basePath||e.getApiField("basePath")),this.resourcePath=this.path,this.path=x(this.path),this.initialize(...arguments)}function ef(e,t){for(let r in t){if(!Object.prototype.hasOwnProperty.call(t,r))continue;let o=r[0].toLowerCase()+r.substring(1),n=new t[r](e);this[o]=n}}function ey(e,t){return function(e){return new ef(e,t)}}em.extend=function(e){let t=this,r=Object.prototype.hasOwnProperty.call(e,"constructor")?e.constructor:function(...e){t.apply(this,e)};return Object.assign(r,t),r.prototype=Object.create(t.prototype),Object.assign(r.prototype,e),r},em.method=function(e){if(void 0!==e.path&&void 0!==e.fullPath)throw Error(`Method spec specified both a 'path' (${e.path}) and a 'fullPath' (${e.fullPath}).`);return function(...t){let r="function"==typeof t[t.length-1]&&t.pop();e.urlParams=function(e){let t=e.match(/\{\w+\}/g);return t?t.map(e=>e.replace(/[{}]/g,"")):[]}(e.fullPath||this.createResourcePathWithSymbols(e.path||""));let o=C(this._makeRequest(t,e,{}),r);return Object.assign(o,ed(this,t,e,o)),o}},em.MAX_BUFFERED_REQUEST_METRICS=100,em.prototype={_stripe:null,path:"",resourcePath:"",basePath:null,initialize(){},requestDataProcessor:null,validateRequest:null,createFullPath(e,t){let r=[this.basePath(t),this.path(t)];if("function"==typeof e){let o=e(t);o&&r.push(o)}else r.push(e);return this._joinUrlParts(r)},createResourcePathWithSymbols(e){return e?`/${this._joinUrlParts([this.resourcePath,e])}`:`/${this.resourcePath}`},_joinUrlParts:e=>e.join("/").replace(/\/{2,}/g,"/"),_getRequestOpts(e,t,r){var o;let n=(t.method||"GET").toUpperCase(),i=t.usage||[],a=t.urlParams||[],s=t.encode||(e=>e),l=!!t.fullPath,u=x(l?t.fullPath:t.path||""),c=l?t.fullPath:this.createResourcePathWithSymbols(t.path),d=[].slice.call(e),h=a.reduce((e,t)=>{let r=d.shift();if("string"!=typeof r)throw Error(`Stripe: Argument "${t}" must be a string, but got: ${r} (on API request to \`${n} ${c}\`)`);return e[t]=r,e},{}),p=s(Object.assign({},w(d),r)),m=A(d),f=m.host||t.host,y=!!t.streaming||!!m.streaming;if(d.filter(e=>null!=e).length)throw Error(`Stripe: Unknown arguments (${d}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options. (on API request to ${n} \`${c}\`)`);let v=l?u(h):this.createFullPath(u,h),P=Object.assign(m.headers,t.headers);t.validator&&t.validator(p,{headers:P});let g="GET"===t.method||"DELETE"===t.method;return{requestMethod:n,requestPath:v,bodyData:g?null:p,queryData:g?p:{},authenticator:null!=(o=m.authenticator)?o:null,headers:P,host:null!=f?f:null,streaming:y,settings:m.settings,usage:i}},_makeRequest(e,t,r){return new Promise((o,n)=>{var i;let a;try{a=this._getRequestOpts(e,t,r)}catch(e){n(e);return}let s=0===Object.keys(a.queryData).length,l=[a.requestPath,s?"":"?",O(a.queryData,D(a.requestPath))].join(""),{headers:u,settings:c}=a;this._stripe._requestSender._request(a.requestMethod,a.host,l,a.bodyData,a.authenticator,{headers:u,settings:c,streaming:a.streaming},a.usage,function(e,r){e?n(e):o(t.transformResponseData?t.transformResponseData(r):r)},null==(i=this.requestDataProcessor)?void 0:i.bind(this))})}};let ev=em.method,eP=em.extend({retrieve:ev({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}"}),list:ev({method:"GET",fullPath:"/v1/financial_connections/accounts",methodType:"list"}),disconnect:ev({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/disconnect"}),listOwners:ev({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}/owners",methodType:"list"}),refresh:ev({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/refresh"}),subscribe:ev({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/subscribe"}),unsubscribe:ev({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/unsubscribe"})}),eg=em.method,eT=em.extend({retrieve:eg({method:"GET",fullPath:"/v1/entitlements/active_entitlements/{id}"}),list:eg({method:"GET",fullPath:"/v1/entitlements/active_entitlements",methodType:"list"})}),e_=em.method,eE=em.extend({create:e_({method:"POST",fullPath:"/v1/billing/alerts"}),retrieve:e_({method:"GET",fullPath:"/v1/billing/alerts/{id}"}),list:e_({method:"GET",fullPath:"/v1/billing/alerts",methodType:"list"}),activate:e_({method:"POST",fullPath:"/v1/billing/alerts/{id}/activate"}),archive:e_({method:"POST",fullPath:"/v1/billing/alerts/{id}/archive"}),deactivate:e_({method:"POST",fullPath:"/v1/billing/alerts/{id}/deactivate"})}),eS=em.method,eb=em.extend({create:eS({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations"}),capture:eS({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/capture"}),expire:eS({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/expire"}),finalizeAmount:eS({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/finalize_amount"}),increment:eS({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/increment"}),respond:eS({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/fraud_challenges/respond"}),reverse:eS({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/reverse"})}),eO=em.method,ex=em.extend({retrieve:eO({method:"GET",fullPath:"/v1/issuing/authorizations/{authorization}"}),update:eO({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}"}),list:eO({method:"GET",fullPath:"/v1/issuing/authorizations",methodType:"list"}),approve:eO({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/approve"}),decline:eO({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/decline"})}),ew=em.method,eA=em.extend({create:ew({method:"POST",fullPath:"/v1/tax/calculations"}),retrieve:ew({method:"GET",fullPath:"/v1/tax/calculations/{calculation}"}),listLineItems:ew({method:"GET",fullPath:"/v1/tax/calculations/{calculation}/line_items",methodType:"list"})}),eR=em.method,eC=em.extend({create:eR({method:"POST",fullPath:"/v1/issuing/cardholders"}),retrieve:eR({method:"GET",fullPath:"/v1/issuing/cardholders/{cardholder}"}),update:eR({method:"POST",fullPath:"/v1/issuing/cardholders/{cardholder}"}),list:eR({method:"GET",fullPath:"/v1/issuing/cardholders",methodType:"list"})}),eG=em.method,eI=em.extend({deliverCard:eG({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/deliver"}),failCard:eG({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/fail"}),returnCard:eG({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/return"}),shipCard:eG({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/ship"}),submitCard:eG({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/submit"})}),ej=em.method,ek=em.extend({create:ej({method:"POST",fullPath:"/v1/issuing/cards"}),retrieve:ej({method:"GET",fullPath:"/v1/issuing/cards/{card}"}),update:ej({method:"POST",fullPath:"/v1/issuing/cards/{card}"}),list:ej({method:"GET",fullPath:"/v1/issuing/cards",methodType:"list"})}),eD=em.method,eN=em.extend({create:eD({method:"POST",fullPath:"/v1/billing_portal/configurations"}),retrieve:eD({method:"GET",fullPath:"/v1/billing_portal/configurations/{configuration}"}),update:eD({method:"POST",fullPath:"/v1/billing_portal/configurations/{configuration}"}),list:eD({method:"GET",fullPath:"/v1/billing_portal/configurations",methodType:"list"})}),eM=em.method,eF=em.extend({create:eM({method:"POST",fullPath:"/v1/terminal/configurations"}),retrieve:eM({method:"GET",fullPath:"/v1/terminal/configurations/{configuration}"}),update:eM({method:"POST",fullPath:"/v1/terminal/configurations/{configuration}"}),list:eM({method:"GET",fullPath:"/v1/terminal/configurations",methodType:"list"}),del:eM({method:"DELETE",fullPath:"/v1/terminal/configurations/{configuration}"})}),eq=em.method,eU=em.extend({create:eq({method:"POST",fullPath:"/v1/test_helpers/confirmation_tokens"})}),eL=em.method,eH=em.extend({create:eL({method:"POST",fullPath:"/v1/terminal/connection_tokens"})}),e$=em.method,ez=em.extend({retrieve:e$({method:"GET",fullPath:"/v1/billing/credit_balance_summary"})}),eB=em.method,eW=em.extend({retrieve:eB({method:"GET",fullPath:"/v1/billing/credit_balance_transactions/{id}"}),list:eB({method:"GET",fullPath:"/v1/billing/credit_balance_transactions",methodType:"list"})}),eK=em.method,eV=em.extend({create:eK({method:"POST",fullPath:"/v1/billing/credit_grants"}),retrieve:eK({method:"GET",fullPath:"/v1/billing/credit_grants/{id}"}),update:eK({method:"POST",fullPath:"/v1/billing/credit_grants/{id}"}),list:eK({method:"GET",fullPath:"/v1/billing/credit_grants",methodType:"list"}),expire:eK({method:"POST",fullPath:"/v1/billing/credit_grants/{id}/expire"}),voidGrant:eK({method:"POST",fullPath:"/v1/billing/credit_grants/{id}/void"})}),eJ=em.method,eQ=em.extend({create:eJ({method:"POST",fullPath:"/v1/treasury/credit_reversals"}),retrieve:eJ({method:"GET",fullPath:"/v1/treasury/credit_reversals/{credit_reversal}"}),list:eJ({method:"GET",fullPath:"/v1/treasury/credit_reversals",methodType:"list"})}),eX=em.method,eY=em.extend({fundCashBalance:eX({method:"POST",fullPath:"/v1/test_helpers/customers/{customer}/fund_cash_balance"})}),eZ=em.method,e1=em.extend({create:eZ({method:"POST",fullPath:"/v1/treasury/debit_reversals"}),retrieve:eZ({method:"GET",fullPath:"/v1/treasury/debit_reversals/{debit_reversal}"}),list:eZ({method:"GET",fullPath:"/v1/treasury/debit_reversals",methodType:"list"})}),e0=em.method,e2=em.extend({create:e0({method:"POST",fullPath:"/v1/issuing/disputes"}),retrieve:e0({method:"GET",fullPath:"/v1/issuing/disputes/{dispute}"}),update:e0({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}"}),list:e0({method:"GET",fullPath:"/v1/issuing/disputes",methodType:"list"}),submit:e0({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}/submit"})}),e8=em.method,e4=em.extend({retrieve:e8({method:"GET",fullPath:"/v1/radar/early_fraud_warnings/{early_fraud_warning}"}),list:e8({method:"GET",fullPath:"/v1/radar/early_fraud_warnings",methodType:"list"})}),e3=em.method,e6=em.extend({create:e3({method:"POST",fullPath:"/v2/core/event_destinations"}),retrieve:e3({method:"GET",fullPath:"/v2/core/event_destinations/{id}"}),update:e3({method:"POST",fullPath:"/v2/core/event_destinations/{id}"}),list:e3({method:"GET",fullPath:"/v2/core/event_destinations",methodType:"list"}),del:e3({method:"DELETE",fullPath:"/v2/core/event_destinations/{id}"}),disable:e3({method:"POST",fullPath:"/v2/core/event_destinations/{id}/disable"}),enable:e3({method:"POST",fullPath:"/v2/core/event_destinations/{id}/enable"}),ping:e3({method:"POST",fullPath:"/v2/core/event_destinations/{id}/ping"})}),e5=em.method,e9=em.extend({retrieve(...e){return e5({method:"GET",fullPath:"/v2/core/events/{id}",transformResponseData:e=>this.addFetchRelatedObjectIfNeeded(e)}).apply(this,e)},list(...e){return e5({method:"GET",fullPath:"/v2/core/events",methodType:"list",transformResponseData:e=>Object.assign(Object.assign({},e),{data:e.data.map(this.addFetchRelatedObjectIfNeeded.bind(this))})}).apply(this,e)},addFetchRelatedObjectIfNeeded(e){return e.related_object&&e.related_object.url?Object.assign(Object.assign({},e),{fetchRelatedObject:()=>e5({method:"GET",fullPath:e.related_object.url}).apply(this,[{stripeAccount:e.context}])}):e}}),e7=em.method,te=em.extend({create:e7({method:"POST",fullPath:"/v1/entitlements/features"}),retrieve:e7({method:"GET",fullPath:"/v1/entitlements/features/{id}"}),update:e7({method:"POST",fullPath:"/v1/entitlements/features/{id}"}),list:e7({method:"GET",fullPath:"/v1/entitlements/features",methodType:"list"})}),tt=em.method,tr=em.extend({create:tt({method:"POST",fullPath:"/v1/treasury/financial_accounts"}),retrieve:tt({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),update:tt({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),list:tt({method:"GET",fullPath:"/v1/treasury/financial_accounts",methodType:"list"}),close:tt({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/close"}),retrieveFeatures:tt({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"}),updateFeatures:tt({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"})}),to=em.method,tn=em.extend({fail:to({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/fail"}),returnInboundTransfer:to({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/return"}),succeed:to({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/succeed"})}),ti=em.method,ta=em.extend({create:ti({method:"POST",fullPath:"/v1/treasury/inbound_transfers"}),retrieve:ti({method:"GET",fullPath:"/v1/treasury/inbound_transfers/{id}"}),list:ti({method:"GET",fullPath:"/v1/treasury/inbound_transfers",methodType:"list"}),cancel:ti({method:"POST",fullPath:"/v1/treasury/inbound_transfers/{inbound_transfer}/cancel"})}),ts=em.method,tl=em.extend({create:ts({method:"POST",fullPath:"/v1/terminal/locations"}),retrieve:ts({method:"GET",fullPath:"/v1/terminal/locations/{location}"}),update:ts({method:"POST",fullPath:"/v1/terminal/locations/{location}"}),list:ts({method:"GET",fullPath:"/v1/terminal/locations",methodType:"list"}),del:ts({method:"DELETE",fullPath:"/v1/terminal/locations/{location}"})}),tu=em.method,tc=em.extend({create:tu({method:"POST",fullPath:"/v1/billing/meter_event_adjustments"})}),td=em.method,th=em.extend({create:td({method:"POST",fullPath:"/v2/billing/meter_event_adjustments"})}),tp=em.method,tm=em.extend({create:tp({method:"POST",fullPath:"/v2/billing/meter_event_session"})}),tf=em.method,ty=em.extend({create:tf({method:"POST",fullPath:"/v2/billing/meter_event_stream",host:"meter-events.stripe.com"})}),tv=em.method,tP=em.extend({create:tv({method:"POST",fullPath:"/v1/billing/meter_events"})}),tg=em.method,tT=em.extend({create:tg({method:"POST",fullPath:"/v2/billing/meter_events"})}),t_=em.method,tE=em.extend({create:t_({method:"POST",fullPath:"/v1/billing/meters"}),retrieve:t_({method:"GET",fullPath:"/v1/billing/meters/{id}"}),update:t_({method:"POST",fullPath:"/v1/billing/meters/{id}"}),list:t_({method:"GET",fullPath:"/v1/billing/meters",methodType:"list"}),deactivate:t_({method:"POST",fullPath:"/v1/billing/meters/{id}/deactivate"}),listEventSummaries:t_({method:"GET",fullPath:"/v1/billing/meters/{id}/event_summaries",methodType:"list"}),reactivate:t_({method:"POST",fullPath:"/v1/billing/meters/{id}/reactivate"})}),tS=em.method,tb=em.extend({create:tS({method:"POST",fullPath:"/v1/climate/orders"}),retrieve:tS({method:"GET",fullPath:"/v1/climate/orders/{order}"}),update:tS({method:"POST",fullPath:"/v1/climate/orders/{order}"}),list:tS({method:"GET",fullPath:"/v1/climate/orders",methodType:"list"}),cancel:tS({method:"POST",fullPath:"/v1/climate/orders/{order}/cancel"})}),tO=em.method,tx=em.extend({update:tO({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}"}),fail:tO({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/fail"}),post:tO({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/post"}),returnOutboundPayment:tO({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/return"})}),tw=em.method,tA=em.extend({create:tw({method:"POST",fullPath:"/v1/treasury/outbound_payments"}),retrieve:tw({method:"GET",fullPath:"/v1/treasury/outbound_payments/{id}"}),list:tw({method:"GET",fullPath:"/v1/treasury/outbound_payments",methodType:"list"}),cancel:tw({method:"POST",fullPath:"/v1/treasury/outbound_payments/{id}/cancel"})}),tR=em.method,tC=em.extend({update:tR({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}"}),fail:tR({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/fail"}),post:tR({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/post"}),returnOutboundTransfer:tR({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/return"})}),tG=em.method,tI=em.extend({create:tG({method:"POST",fullPath:"/v1/treasury/outbound_transfers"}),retrieve:tG({method:"GET",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}"}),list:tG({method:"GET",fullPath:"/v1/treasury/outbound_transfers",methodType:"list"}),cancel:tG({method:"POST",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}/cancel"})}),tj=em.method,tk=em.extend({activate:tj({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/activate"}),deactivate:tj({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/deactivate"}),reject:tj({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/reject"})}),tD=em.method,tN=em.extend({create:tD({method:"POST",fullPath:"/v1/issuing/personalization_designs"}),retrieve:tD({method:"GET",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),update:tD({method:"POST",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),list:tD({method:"GET",fullPath:"/v1/issuing/personalization_designs",methodType:"list"})}),tM=em.method,tF=em.extend({retrieve:tM({method:"GET",fullPath:"/v1/issuing/physical_bundles/{physical_bundle}"}),list:tM({method:"GET",fullPath:"/v1/issuing/physical_bundles",methodType:"list"})}),tq=em.method,tU=em.extend({retrieve:tq({method:"GET",fullPath:"/v1/climate/products/{product}"}),list:tq({method:"GET",fullPath:"/v1/climate/products",methodType:"list"})}),tL=em.method,tH=em.extend({presentPaymentMethod:tL({method:"POST",fullPath:"/v1/test_helpers/terminal/readers/{reader}/present_payment_method"}),succeedInputCollection:tL({method:"POST",fullPath:"/v1/test_helpers/terminal/readers/{reader}/succeed_input_collection"}),timeoutInputCollection:tL({method:"POST",fullPath:"/v1/test_helpers/terminal/readers/{reader}/timeout_input_collection"})}),t$=em.method,tz=em.extend({create:t$({method:"POST",fullPath:"/v1/terminal/readers"}),retrieve:t$({method:"GET",fullPath:"/v1/terminal/readers/{reader}"}),update:t$({method:"POST",fullPath:"/v1/terminal/readers/{reader}"}),list:t$({method:"GET",fullPath:"/v1/terminal/readers",methodType:"list"}),del:t$({method:"DELETE",fullPath:"/v1/terminal/readers/{reader}"}),cancelAction:t$({method:"POST",fullPath:"/v1/terminal/readers/{reader}/cancel_action"}),collectInputs:t$({method:"POST",fullPath:"/v1/terminal/readers/{reader}/collect_inputs"}),processPaymentIntent:t$({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_payment_intent"}),processSetupIntent:t$({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_setup_intent"}),refundPayment:t$({method:"POST",fullPath:"/v1/terminal/readers/{reader}/refund_payment"}),setReaderDisplay:t$({method:"POST",fullPath:"/v1/terminal/readers/{reader}/set_reader_display"})}),tB=em.method,tW=em.extend({create:tB({method:"POST",fullPath:"/v1/test_helpers/treasury/received_credits"})}),tK=em.method,tV=em.extend({retrieve:tK({method:"GET",fullPath:"/v1/treasury/received_credits/{id}"}),list:tK({method:"GET",fullPath:"/v1/treasury/received_credits",methodType:"list"})}),tJ=em.method,tQ=em.extend({create:tJ({method:"POST",fullPath:"/v1/test_helpers/treasury/received_debits"})}),tX=em.method,tY=em.extend({retrieve:tX({method:"GET",fullPath:"/v1/treasury/received_debits/{id}"}),list:tX({method:"GET",fullPath:"/v1/treasury/received_debits",methodType:"list"})}),tZ=em.method,t1=em.extend({expire:tZ({method:"POST",fullPath:"/v1/test_helpers/refunds/{refund}/expire"})}),t0=em.method,t2=em.extend({create:t0({method:"POST",fullPath:"/v1/tax/registrations"}),retrieve:t0({method:"GET",fullPath:"/v1/tax/registrations/{id}"}),update:t0({method:"POST",fullPath:"/v1/tax/registrations/{id}"}),list:t0({method:"GET",fullPath:"/v1/tax/registrations",methodType:"list"})}),t8=em.method,t4=em.extend({create:t8({method:"POST",fullPath:"/v1/reporting/report_runs"}),retrieve:t8({method:"GET",fullPath:"/v1/reporting/report_runs/{report_run}"}),list:t8({method:"GET",fullPath:"/v1/reporting/report_runs",methodType:"list"})}),t3=em.method,t6=em.extend({retrieve:t3({method:"GET",fullPath:"/v1/reporting/report_types/{report_type}"}),list:t3({method:"GET",fullPath:"/v1/reporting/report_types",methodType:"list"})}),t5=em.method,t9=em.extend({create:t5({method:"POST",fullPath:"/v1/forwarding/requests"}),retrieve:t5({method:"GET",fullPath:"/v1/forwarding/requests/{id}"}),list:t5({method:"GET",fullPath:"/v1/forwarding/requests",methodType:"list"})}),t7=em.method,re=em.extend({retrieve:t7({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs/{scheduled_query_run}"}),list:t7({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs",methodType:"list"})}),rt=em.method,rr=em.extend({create:rt({method:"POST",fullPath:"/v1/apps/secrets"}),list:rt({method:"GET",fullPath:"/v1/apps/secrets",methodType:"list"}),deleteWhere:rt({method:"POST",fullPath:"/v1/apps/secrets/delete"}),find:rt({method:"GET",fullPath:"/v1/apps/secrets/find"})}),ro=em.method,rn=em.extend({create:ro({method:"POST",fullPath:"/v1/billing_portal/sessions"})}),ri=em.method,ra=em.extend({create:ri({method:"POST",fullPath:"/v1/checkout/sessions"}),retrieve:ri({method:"GET",fullPath:"/v1/checkout/sessions/{session}"}),update:ri({method:"POST",fullPath:"/v1/checkout/sessions/{session}"}),list:ri({method:"GET",fullPath:"/v1/checkout/sessions",methodType:"list"}),expire:ri({method:"POST",fullPath:"/v1/checkout/sessions/{session}/expire"}),listLineItems:ri({method:"GET",fullPath:"/v1/checkout/sessions/{session}/line_items",methodType:"list"})}),rs=em.method,rl=em.extend({create:rs({method:"POST",fullPath:"/v1/financial_connections/sessions"}),retrieve:rs({method:"GET",fullPath:"/v1/financial_connections/sessions/{session}"})}),ru=em.method,rc=em.extend({retrieve:ru({method:"GET",fullPath:"/v1/tax/settings"}),update:ru({method:"POST",fullPath:"/v1/tax/settings"})}),rd=em.method,rh=em.extend({retrieve:rd({method:"GET",fullPath:"/v1/climate/suppliers/{supplier}"}),list:rd({method:"GET",fullPath:"/v1/climate/suppliers",methodType:"list"})}),rp=em.method,rm=em.extend({create:rp({method:"POST",fullPath:"/v1/test_helpers/test_clocks"}),retrieve:rp({method:"GET",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),list:rp({method:"GET",fullPath:"/v1/test_helpers/test_clocks",methodType:"list"}),del:rp({method:"DELETE",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),advance:rp({method:"POST",fullPath:"/v1/test_helpers/test_clocks/{test_clock}/advance"})}),rf=em.method,ry=em.extend({retrieve:rf({method:"GET",fullPath:"/v1/issuing/tokens/{token}"}),update:rf({method:"POST",fullPath:"/v1/issuing/tokens/{token}"}),list:rf({method:"GET",fullPath:"/v1/issuing/tokens",methodType:"list"})}),rv=em.method,rP=em.extend({retrieve:rv({method:"GET",fullPath:"/v1/treasury/transaction_entries/{id}"}),list:rv({method:"GET",fullPath:"/v1/treasury/transaction_entries",methodType:"list"})}),rg=em.method,rT=em.extend({createForceCapture:rg({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_force_capture"}),createUnlinkedRefund:rg({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_unlinked_refund"}),refund:rg({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/{transaction}/refund"})}),r_=em.method,rE=em.extend({retrieve:r_({method:"GET",fullPath:"/v1/financial_connections/transactions/{transaction}"}),list:r_({method:"GET",fullPath:"/v1/financial_connections/transactions",methodType:"list"})}),rS=em.method,rb=em.extend({retrieve:rS({method:"GET",fullPath:"/v1/issuing/transactions/{transaction}"}),update:rS({method:"POST",fullPath:"/v1/issuing/transactions/{transaction}"}),list:rS({method:"GET",fullPath:"/v1/issuing/transactions",methodType:"list"})}),rO=em.method,rx=em.extend({retrieve:rO({method:"GET",fullPath:"/v1/tax/transactions/{transaction}"}),createFromCalculation:rO({method:"POST",fullPath:"/v1/tax/transactions/create_from_calculation"}),createReversal:rO({method:"POST",fullPath:"/v1/tax/transactions/create_reversal"}),listLineItems:rO({method:"GET",fullPath:"/v1/tax/transactions/{transaction}/line_items",methodType:"list"})}),rw=em.method,rA=em.extend({retrieve:rw({method:"GET",fullPath:"/v1/treasury/transactions/{id}"}),list:rw({method:"GET",fullPath:"/v1/treasury/transactions",methodType:"list"})}),rR=em.method,rC=em.extend({create:rR({method:"POST",fullPath:"/v1/radar/value_list_items"}),retrieve:rR({method:"GET",fullPath:"/v1/radar/value_list_items/{item}"}),list:rR({method:"GET",fullPath:"/v1/radar/value_list_items",methodType:"list"}),del:rR({method:"DELETE",fullPath:"/v1/radar/value_list_items/{item}"})}),rG=em.method,rI=em.extend({create:rG({method:"POST",fullPath:"/v1/radar/value_lists"}),retrieve:rG({method:"GET",fullPath:"/v1/radar/value_lists/{value_list}"}),update:rG({method:"POST",fullPath:"/v1/radar/value_lists/{value_list}"}),list:rG({method:"GET",fullPath:"/v1/radar/value_lists",methodType:"list"}),del:rG({method:"DELETE",fullPath:"/v1/radar/value_lists/{value_list}"})}),rj=em.method,rk=em.extend({retrieve:rj({method:"GET",fullPath:"/v1/identity/verification_reports/{report}"}),list:rj({method:"GET",fullPath:"/v1/identity/verification_reports",methodType:"list"})}),rD=em.method,rN=em.extend({create:rD({method:"POST",fullPath:"/v1/identity/verification_sessions"}),retrieve:rD({method:"GET",fullPath:"/v1/identity/verification_sessions/{session}"}),update:rD({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}"}),list:rD({method:"GET",fullPath:"/v1/identity/verification_sessions",methodType:"list"}),cancel:rD({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/cancel"}),redact:rD({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/redact"})}),rM=em.method,rF=em.extend({create:rM({method:"POST",fullPath:"/v1/accounts"}),retrieve(e,...t){return"string"==typeof e?rM({method:"GET",fullPath:"/v1/accounts/{id}"}).apply(this,[e,...t]):(null==e&&[].shift.apply([e,...t]),rM({method:"GET",fullPath:"/v1/account"}).apply(this,[e,...t]))},update:rM({method:"POST",fullPath:"/v1/accounts/{account}"}),list:rM({method:"GET",fullPath:"/v1/accounts",methodType:"list"}),del:rM({method:"DELETE",fullPath:"/v1/accounts/{account}"}),createExternalAccount:rM({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts"}),createLoginLink:rM({method:"POST",fullPath:"/v1/accounts/{account}/login_links"}),createPerson:rM({method:"POST",fullPath:"/v1/accounts/{account}/persons"}),deleteExternalAccount:rM({method:"DELETE",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),deletePerson:rM({method:"DELETE",fullPath:"/v1/accounts/{account}/persons/{person}"}),listCapabilities:rM({method:"GET",fullPath:"/v1/accounts/{account}/capabilities",methodType:"list"}),listExternalAccounts:rM({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts",methodType:"list"}),listPersons:rM({method:"GET",fullPath:"/v1/accounts/{account}/persons",methodType:"list"}),reject:rM({method:"POST",fullPath:"/v1/accounts/{account}/reject"}),retrieveCurrent:rM({method:"GET",fullPath:"/v1/account"}),retrieveCapability:rM({method:"GET",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),retrieveExternalAccount:rM({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),retrievePerson:rM({method:"GET",fullPath:"/v1/accounts/{account}/persons/{person}"}),updateCapability:rM({method:"POST",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),updateExternalAccount:rM({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),updatePerson:rM({method:"POST",fullPath:"/v1/accounts/{account}/persons/{person}"})}),rq=em.method,rU=em.extend({create:rq({method:"POST",fullPath:"/v1/account_links"})}),rL=em.method,rH=em.extend({create:rL({method:"POST",fullPath:"/v1/account_sessions"})}),r$=em.method,rz=em.extend({create:r$({method:"POST",fullPath:"/v1/apple_pay/domains"}),retrieve:r$({method:"GET",fullPath:"/v1/apple_pay/domains/{domain}"}),list:r$({method:"GET",fullPath:"/v1/apple_pay/domains",methodType:"list"}),del:r$({method:"DELETE",fullPath:"/v1/apple_pay/domains/{domain}"})}),rB=em.method,rW=em.extend({retrieve:rB({method:"GET",fullPath:"/v1/application_fees/{id}"}),list:rB({method:"GET",fullPath:"/v1/application_fees",methodType:"list"}),createRefund:rB({method:"POST",fullPath:"/v1/application_fees/{id}/refunds"}),listRefunds:rB({method:"GET",fullPath:"/v1/application_fees/{id}/refunds",methodType:"list"}),retrieveRefund:rB({method:"GET",fullPath:"/v1/application_fees/{fee}/refunds/{id}"}),updateRefund:rB({method:"POST",fullPath:"/v1/application_fees/{fee}/refunds/{id}"})}),rK=em.method,rV=em.extend({retrieve:rK({method:"GET",fullPath:"/v1/balance"})}),rJ=em.method,rQ=em.extend({retrieve:rJ({method:"GET",fullPath:"/v1/balance_transactions/{id}"}),list:rJ({method:"GET",fullPath:"/v1/balance_transactions",methodType:"list"})}),rX=em.method,rY=em.extend({create:rX({method:"POST",fullPath:"/v1/charges"}),retrieve:rX({method:"GET",fullPath:"/v1/charges/{charge}"}),update:rX({method:"POST",fullPath:"/v1/charges/{charge}"}),list:rX({method:"GET",fullPath:"/v1/charges",methodType:"list"}),capture:rX({method:"POST",fullPath:"/v1/charges/{charge}/capture"}),search:rX({method:"GET",fullPath:"/v1/charges/search",methodType:"search"})}),rZ=em.method,r1=em.extend({retrieve:rZ({method:"GET",fullPath:"/v1/confirmation_tokens/{confirmation_token}"})}),r0=em.method,r2=em.extend({retrieve:r0({method:"GET",fullPath:"/v1/country_specs/{country}"}),list:r0({method:"GET",fullPath:"/v1/country_specs",methodType:"list"})}),r8=em.method,r4=em.extend({create:r8({method:"POST",fullPath:"/v1/coupons"}),retrieve:r8({method:"GET",fullPath:"/v1/coupons/{coupon}"}),update:r8({method:"POST",fullPath:"/v1/coupons/{coupon}"}),list:r8({method:"GET",fullPath:"/v1/coupons",methodType:"list"}),del:r8({method:"DELETE",fullPath:"/v1/coupons/{coupon}"})}),r3=em.method,r6=em.extend({create:r3({method:"POST",fullPath:"/v1/credit_notes"}),retrieve:r3({method:"GET",fullPath:"/v1/credit_notes/{id}"}),update:r3({method:"POST",fullPath:"/v1/credit_notes/{id}"}),list:r3({method:"GET",fullPath:"/v1/credit_notes",methodType:"list"}),listLineItems:r3({method:"GET",fullPath:"/v1/credit_notes/{credit_note}/lines",methodType:"list"}),listPreviewLineItems:r3({method:"GET",fullPath:"/v1/credit_notes/preview/lines",methodType:"list"}),preview:r3({method:"GET",fullPath:"/v1/credit_notes/preview"}),voidCreditNote:r3({method:"POST",fullPath:"/v1/credit_notes/{id}/void"})}),r5=em.method,r9=em.extend({create:r5({method:"POST",fullPath:"/v1/customer_sessions"})}),r7=em.method,oe=em.extend({create:r7({method:"POST",fullPath:"/v1/customers"}),retrieve:r7({method:"GET",fullPath:"/v1/customers/{customer}"}),update:r7({method:"POST",fullPath:"/v1/customers/{customer}"}),list:r7({method:"GET",fullPath:"/v1/customers",methodType:"list"}),del:r7({method:"DELETE",fullPath:"/v1/customers/{customer}"}),createBalanceTransaction:r7({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions"}),createFundingInstructions:r7({method:"POST",fullPath:"/v1/customers/{customer}/funding_instructions"}),createSource:r7({method:"POST",fullPath:"/v1/customers/{customer}/sources"}),createTaxId:r7({method:"POST",fullPath:"/v1/customers/{customer}/tax_ids"}),deleteDiscount:r7({method:"DELETE",fullPath:"/v1/customers/{customer}/discount"}),deleteSource:r7({method:"DELETE",fullPath:"/v1/customers/{customer}/sources/{id}"}),deleteTaxId:r7({method:"DELETE",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),listBalanceTransactions:r7({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions",methodType:"list"}),listCashBalanceTransactions:r7({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions",methodType:"list"}),listPaymentMethods:r7({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods",methodType:"list"}),listSources:r7({method:"GET",fullPath:"/v1/customers/{customer}/sources",methodType:"list"}),listTaxIds:r7({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids",methodType:"list"}),retrieveBalanceTransaction:r7({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),retrieveCashBalance:r7({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance"}),retrieveCashBalanceTransaction:r7({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions/{transaction}"}),retrievePaymentMethod:r7({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods/{payment_method}"}),retrieveSource:r7({method:"GET",fullPath:"/v1/customers/{customer}/sources/{id}"}),retrieveTaxId:r7({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),search:r7({method:"GET",fullPath:"/v1/customers/search",methodType:"search"}),updateBalanceTransaction:r7({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),updateCashBalance:r7({method:"POST",fullPath:"/v1/customers/{customer}/cash_balance"}),updateSource:r7({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}"}),verifySource:r7({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}/verify"})}),ot=em.method,or=em.extend({retrieve:ot({method:"GET",fullPath:"/v1/disputes/{dispute}"}),update:ot({method:"POST",fullPath:"/v1/disputes/{dispute}"}),list:ot({method:"GET",fullPath:"/v1/disputes",methodType:"list"}),close:ot({method:"POST",fullPath:"/v1/disputes/{dispute}/close"})}),oo=em.method,on=em.extend({create:oo({method:"POST",fullPath:"/v1/ephemeral_keys",validator:(e,t)=>{if(!t.headers||!t.headers["Stripe-Version"])throw Error("Passing apiVersion in a separate options hash is required to create an ephemeral key. See https://stripe.com/docs/api/versioning?lang=node")}}),del:oo({method:"DELETE",fullPath:"/v1/ephemeral_keys/{key}"})}),oi=em.method,oa=em.extend({retrieve:oi({method:"GET",fullPath:"/v1/events/{id}"}),list:oi({method:"GET",fullPath:"/v1/events",methodType:"list"})}),os=em.method,ol=em.extend({retrieve:os({method:"GET",fullPath:"/v1/exchange_rates/{rate_id}"}),list:os({method:"GET",fullPath:"/v1/exchange_rates",methodType:"list"})}),ou=em.method,oc=em.extend({create:ou({method:"POST",fullPath:"/v1/file_links"}),retrieve:ou({method:"GET",fullPath:"/v1/file_links/{link}"}),update:ou({method:"POST",fullPath:"/v1/file_links/{link}"}),list:ou({method:"GET",fullPath:"/v1/file_links",methodType:"list"})}),od=(e,t,r)=>{let o=(Math.round(1e16*Math.random())+Math.round(1e16*Math.random())).toString();r["Content-Type"]=`multipart/form-data; boundary=${o}`;let n=new TextEncoder,i=new Uint8Array(0),a=n.encode("\r\n");function s(e){let t=i,r=e instanceof Uint8Array?e:new Uint8Array(n.encode(e));(i=new Uint8Array(t.length+r.length+2)).set(t),i.set(r,t.length),i.set(a,i.length-2)}function l(e){return`"${e.replace(/"|"/g,"%22").replace(/\r\n|\r|\n/g," ")}"`}let u=function(e){let t={},r=(e,o)=>{Object.entries(e).forEach(([e,n])=>{let i=o?`${o}[${e}]`:e;if(function(e){let t=typeof e;return("function"===t||"object"===t)&&!!e}(n))if(!(n instanceof Uint8Array)&&!Object.prototype.hasOwnProperty.call(n,"data"))return r(n,i);else t[i]=n;else t[i]=String(n)})};return r(e,null),t}(t);for(let e in u){if(!Object.prototype.hasOwnProperty.call(u,e))continue;let t=u[e];s(`--${o}`),Object.prototype.hasOwnProperty.call(t,"data")?(s(`Content-Disposition: form-data; name=${l(e)}; filename=${l(t.name||"blob")}`),s(`Content-Type: ${t.type||"application/octet-stream"}`),s(""),s(t.data)):(s(`Content-Disposition: form-data; name=${l(e)}`),s(""),s(t))}return s(`--${o}--`),i},oh=em.method,op=em.extend({create:oh({method:"POST",fullPath:"/v1/files",headers:{"Content-Type":"multipart/form-data"},host:"files.stripe.com"}),retrieve:oh({method:"GET",fullPath:"/v1/files/{file}"}),list:oh({method:"GET",fullPath:"/v1/files",methodType:"list"}),requestDataProcessor:function(e,t,r,o){if(t=t||{},"POST"!==e)return o(null,O(t));this._stripe._platformFunctions.tryBufferData(t).then(t=>o(null,od(e,t,r))).catch(e=>o(e,null))}}),om=em.method,of=em.extend({create:om({method:"POST",fullPath:"/v1/invoiceitems"}),retrieve:om({method:"GET",fullPath:"/v1/invoiceitems/{invoiceitem}"}),update:om({method:"POST",fullPath:"/v1/invoiceitems/{invoiceitem}"}),list:om({method:"GET",fullPath:"/v1/invoiceitems",methodType:"list"}),del:om({method:"DELETE",fullPath:"/v1/invoiceitems/{invoiceitem}"})}),oy=em.method,ov=em.extend({retrieve:oy({method:"GET",fullPath:"/v1/invoice_payments/{invoice_payment}"}),list:oy({method:"GET",fullPath:"/v1/invoice_payments",methodType:"list"})}),oP=em.method,og=em.extend({retrieve:oP({method:"GET",fullPath:"/v1/invoice_rendering_templates/{template}"}),list:oP({method:"GET",fullPath:"/v1/invoice_rendering_templates",methodType:"list"}),archive:oP({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/archive"}),unarchive:oP({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/unarchive"})}),oT=em.method,o_=em.extend({create:oT({method:"POST",fullPath:"/v1/invoices"}),retrieve:oT({method:"GET",fullPath:"/v1/invoices/{invoice}"}),update:oT({method:"POST",fullPath:"/v1/invoices/{invoice}"}),list:oT({method:"GET",fullPath:"/v1/invoices",methodType:"list"}),del:oT({method:"DELETE",fullPath:"/v1/invoices/{invoice}"}),addLines:oT({method:"POST",fullPath:"/v1/invoices/{invoice}/add_lines"}),attachPayment:oT({method:"POST",fullPath:"/v1/invoices/{invoice}/attach_payment"}),createPreview:oT({method:"POST",fullPath:"/v1/invoices/create_preview"}),finalizeInvoice:oT({method:"POST",fullPath:"/v1/invoices/{invoice}/finalize"}),listLineItems:oT({method:"GET",fullPath:"/v1/invoices/{invoice}/lines",methodType:"list"}),markUncollectible:oT({method:"POST",fullPath:"/v1/invoices/{invoice}/mark_uncollectible"}),pay:oT({method:"POST",fullPath:"/v1/invoices/{invoice}/pay"}),removeLines:oT({method:"POST",fullPath:"/v1/invoices/{invoice}/remove_lines"}),search:oT({method:"GET",fullPath:"/v1/invoices/search",methodType:"search"}),sendInvoice:oT({method:"POST",fullPath:"/v1/invoices/{invoice}/send"}),updateLines:oT({method:"POST",fullPath:"/v1/invoices/{invoice}/update_lines"}),updateLineItem:oT({method:"POST",fullPath:"/v1/invoices/{invoice}/lines/{line_item_id}"}),voidInvoice:oT({method:"POST",fullPath:"/v1/invoices/{invoice}/void"})}),oE=em.method,oS=em.extend({retrieve:oE({method:"GET",fullPath:"/v1/mandates/{mandate}"})}),ob=em.method,oO="connect.stripe.com",ox=em.extend({basePath:"/",authorizeUrl(e,t){e=e||{};let r="oauth/authorize";return(t=t||{}).express&&(r=`express/${r}`),e.response_type||(e.response_type="code"),e.client_id||(e.client_id=this._stripe.getClientId()),e.scope||(e.scope="read_write"),`https://${oO}/${r}?${O(e)}`},token:ob({method:"POST",path:"oauth/token",host:oO}),deauthorize(e,...t){return e.client_id||(e.client_id=this._stripe.getClientId()),ob({method:"POST",path:"oauth/deauthorize",host:oO}).apply(this,[e,...t])}}),ow=em.method,oA=em.extend({create:ow({method:"POST",fullPath:"/v1/payment_intents"}),retrieve:ow({method:"GET",fullPath:"/v1/payment_intents/{intent}"}),update:ow({method:"POST",fullPath:"/v1/payment_intents/{intent}"}),list:ow({method:"GET",fullPath:"/v1/payment_intents",methodType:"list"}),applyCustomerBalance:ow({method:"POST",fullPath:"/v1/payment_intents/{intent}/apply_customer_balance"}),cancel:ow({method:"POST",fullPath:"/v1/payment_intents/{intent}/cancel"}),capture:ow({method:"POST",fullPath:"/v1/payment_intents/{intent}/capture"}),confirm:ow({method:"POST",fullPath:"/v1/payment_intents/{intent}/confirm"}),incrementAuthorization:ow({method:"POST",fullPath:"/v1/payment_intents/{intent}/increment_authorization"}),search:ow({method:"GET",fullPath:"/v1/payment_intents/search",methodType:"search"}),verifyMicrodeposits:ow({method:"POST",fullPath:"/v1/payment_intents/{intent}/verify_microdeposits"})}),oR=em.method,oC=em.extend({create:oR({method:"POST",fullPath:"/v1/payment_links"}),retrieve:oR({method:"GET",fullPath:"/v1/payment_links/{payment_link}"}),update:oR({method:"POST",fullPath:"/v1/payment_links/{payment_link}"}),list:oR({method:"GET",fullPath:"/v1/payment_links",methodType:"list"}),listLineItems:oR({method:"GET",fullPath:"/v1/payment_links/{payment_link}/line_items",methodType:"list"})}),oG=em.method,oI=em.extend({create:oG({method:"POST",fullPath:"/v1/payment_method_configurations"}),retrieve:oG({method:"GET",fullPath:"/v1/payment_method_configurations/{configuration}"}),update:oG({method:"POST",fullPath:"/v1/payment_method_configurations/{configuration}"}),list:oG({method:"GET",fullPath:"/v1/payment_method_configurations",methodType:"list"})}),oj=em.method,ok=em.extend({create:oj({method:"POST",fullPath:"/v1/payment_method_domains"}),retrieve:oj({method:"GET",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),update:oj({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),list:oj({method:"GET",fullPath:"/v1/payment_method_domains",methodType:"list"}),validate:oj({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}/validate"})}),oD=em.method,oN=em.extend({create:oD({method:"POST",fullPath:"/v1/payment_methods"}),retrieve:oD({method:"GET",fullPath:"/v1/payment_methods/{payment_method}"}),update:oD({method:"POST",fullPath:"/v1/payment_methods/{payment_method}"}),list:oD({method:"GET",fullPath:"/v1/payment_methods",methodType:"list"}),attach:oD({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/attach"}),detach:oD({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/detach"})}),oM=em.method,oF=em.extend({create:oM({method:"POST",fullPath:"/v1/payouts"}),retrieve:oM({method:"GET",fullPath:"/v1/payouts/{payout}"}),update:oM({method:"POST",fullPath:"/v1/payouts/{payout}"}),list:oM({method:"GET",fullPath:"/v1/payouts",methodType:"list"}),cancel:oM({method:"POST",fullPath:"/v1/payouts/{payout}/cancel"}),reverse:oM({method:"POST",fullPath:"/v1/payouts/{payout}/reverse"})}),oq=em.method,oU=em.extend({create:oq({method:"POST",fullPath:"/v1/plans"}),retrieve:oq({method:"GET",fullPath:"/v1/plans/{plan}"}),update:oq({method:"POST",fullPath:"/v1/plans/{plan}"}),list:oq({method:"GET",fullPath:"/v1/plans",methodType:"list"}),del:oq({method:"DELETE",fullPath:"/v1/plans/{plan}"})}),oL=em.method,oH=em.extend({create:oL({method:"POST",fullPath:"/v1/prices"}),retrieve:oL({method:"GET",fullPath:"/v1/prices/{price}"}),update:oL({method:"POST",fullPath:"/v1/prices/{price}"}),list:oL({method:"GET",fullPath:"/v1/prices",methodType:"list"}),search:oL({method:"GET",fullPath:"/v1/prices/search",methodType:"search"})}),o$=em.method,oz=em.extend({create:o$({method:"POST",fullPath:"/v1/products"}),retrieve:o$({method:"GET",fullPath:"/v1/products/{id}"}),update:o$({method:"POST",fullPath:"/v1/products/{id}"}),list:o$({method:"GET",fullPath:"/v1/products",methodType:"list"}),del:o$({method:"DELETE",fullPath:"/v1/products/{id}"}),createFeature:o$({method:"POST",fullPath:"/v1/products/{product}/features"}),deleteFeature:o$({method:"DELETE",fullPath:"/v1/products/{product}/features/{id}"}),listFeatures:o$({method:"GET",fullPath:"/v1/products/{product}/features",methodType:"list"}),retrieveFeature:o$({method:"GET",fullPath:"/v1/products/{product}/features/{id}"}),search:o$({method:"GET",fullPath:"/v1/products/search",methodType:"search"})}),oB=em.method,oW=em.extend({create:oB({method:"POST",fullPath:"/v1/promotion_codes"}),retrieve:oB({method:"GET",fullPath:"/v1/promotion_codes/{promotion_code}"}),update:oB({method:"POST",fullPath:"/v1/promotion_codes/{promotion_code}"}),list:oB({method:"GET",fullPath:"/v1/promotion_codes",methodType:"list"})}),oK=em.method,oV=em.extend({create:oK({method:"POST",fullPath:"/v1/quotes"}),retrieve:oK({method:"GET",fullPath:"/v1/quotes/{quote}"}),update:oK({method:"POST",fullPath:"/v1/quotes/{quote}"}),list:oK({method:"GET",fullPath:"/v1/quotes",methodType:"list"}),accept:oK({method:"POST",fullPath:"/v1/quotes/{quote}/accept"}),cancel:oK({method:"POST",fullPath:"/v1/quotes/{quote}/cancel"}),finalizeQuote:oK({method:"POST",fullPath:"/v1/quotes/{quote}/finalize"}),listComputedUpfrontLineItems:oK({method:"GET",fullPath:"/v1/quotes/{quote}/computed_upfront_line_items",methodType:"list"}),listLineItems:oK({method:"GET",fullPath:"/v1/quotes/{quote}/line_items",methodType:"list"}),pdf:oK({method:"GET",fullPath:"/v1/quotes/{quote}/pdf",host:"files.stripe.com",streaming:!0})}),oJ=em.method,oQ=em.extend({create:oJ({method:"POST",fullPath:"/v1/refunds"}),retrieve:oJ({method:"GET",fullPath:"/v1/refunds/{refund}"}),update:oJ({method:"POST",fullPath:"/v1/refunds/{refund}"}),list:oJ({method:"GET",fullPath:"/v1/refunds",methodType:"list"}),cancel:oJ({method:"POST",fullPath:"/v1/refunds/{refund}/cancel"})}),oX=em.method,oY=em.extend({retrieve:oX({method:"GET",fullPath:"/v1/reviews/{review}"}),list:oX({method:"GET",fullPath:"/v1/reviews",methodType:"list"}),approve:oX({method:"POST",fullPath:"/v1/reviews/{review}/approve"})}),oZ=em.method,o1=em.extend({list:oZ({method:"GET",fullPath:"/v1/setup_attempts",methodType:"list"})}),o0=em.method,o2=em.extend({create:o0({method:"POST",fullPath:"/v1/setup_intents"}),retrieve:o0({method:"GET",fullPath:"/v1/setup_intents/{intent}"}),update:o0({method:"POST",fullPath:"/v1/setup_intents/{intent}"}),list:o0({method:"GET",fullPath:"/v1/setup_intents",methodType:"list"}),cancel:o0({method:"POST",fullPath:"/v1/setup_intents/{intent}/cancel"}),confirm:o0({method:"POST",fullPath:"/v1/setup_intents/{intent}/confirm"}),verifyMicrodeposits:o0({method:"POST",fullPath:"/v1/setup_intents/{intent}/verify_microdeposits"})}),o8=em.method,o4=em.extend({create:o8({method:"POST",fullPath:"/v1/shipping_rates"}),retrieve:o8({method:"GET",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),update:o8({method:"POST",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),list:o8({method:"GET",fullPath:"/v1/shipping_rates",methodType:"list"})}),o3=em.method,o6=em.extend({create:o3({method:"POST",fullPath:"/v1/sources"}),retrieve:o3({method:"GET",fullPath:"/v1/sources/{source}"}),update:o3({method:"POST",fullPath:"/v1/sources/{source}"}),listSourceTransactions:o3({method:"GET",fullPath:"/v1/sources/{source}/source_transactions",methodType:"list"}),verify:o3({method:"POST",fullPath:"/v1/sources/{source}/verify"})}),o5=em.method,o9=em.extend({create:o5({method:"POST",fullPath:"/v1/subscription_items"}),retrieve:o5({method:"GET",fullPath:"/v1/subscription_items/{item}"}),update:o5({method:"POST",fullPath:"/v1/subscription_items/{item}"}),list:o5({method:"GET",fullPath:"/v1/subscription_items",methodType:"list"}),del:o5({method:"DELETE",fullPath:"/v1/subscription_items/{item}"})}),o7=em.method,ne=em.extend({create:o7({method:"POST",fullPath:"/v1/subscription_schedules"}),retrieve:o7({method:"GET",fullPath:"/v1/subscription_schedules/{schedule}"}),update:o7({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}"}),list:o7({method:"GET",fullPath:"/v1/subscription_schedules",methodType:"list"}),cancel:o7({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/cancel"}),release:o7({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/release"})}),nt=em.method,nr=em.extend({create:nt({method:"POST",fullPath:"/v1/subscriptions"}),retrieve:nt({method:"GET",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),update:nt({method:"POST",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),list:nt({method:"GET",fullPath:"/v1/subscriptions",methodType:"list"}),cancel:nt({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),deleteDiscount:nt({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}/discount"}),resume:nt({method:"POST",fullPath:"/v1/subscriptions/{subscription}/resume"}),search:nt({method:"GET",fullPath:"/v1/subscriptions/search",methodType:"search"})}),no=em.method,nn=em.extend({retrieve:no({method:"GET",fullPath:"/v1/tax_codes/{id}"}),list:no({method:"GET",fullPath:"/v1/tax_codes",methodType:"list"})}),ni=em.method,na=em.extend({create:ni({method:"POST",fullPath:"/v1/tax_ids"}),retrieve:ni({method:"GET",fullPath:"/v1/tax_ids/{id}"}),list:ni({method:"GET",fullPath:"/v1/tax_ids",methodType:"list"}),del:ni({method:"DELETE",fullPath:"/v1/tax_ids/{id}"})}),ns=em.method,nl=em.extend({create:ns({method:"POST",fullPath:"/v1/tax_rates"}),retrieve:ns({method:"GET",fullPath:"/v1/tax_rates/{tax_rate}"}),update:ns({method:"POST",fullPath:"/v1/tax_rates/{tax_rate}"}),list:ns({method:"GET",fullPath:"/v1/tax_rates",methodType:"list"})}),nu=em.method,nc=em.extend({create:nu({method:"POST",fullPath:"/v1/tokens"}),retrieve:nu({method:"GET",fullPath:"/v1/tokens/{token}"})}),nd=em.method,nh=em.extend({create:nd({method:"POST",fullPath:"/v1/topups"}),retrieve:nd({method:"GET",fullPath:"/v1/topups/{topup}"}),update:nd({method:"POST",fullPath:"/v1/topups/{topup}"}),list:nd({method:"GET",fullPath:"/v1/topups",methodType:"list"}),cancel:nd({method:"POST",fullPath:"/v1/topups/{topup}/cancel"})}),np=em.method,nm=em.extend({create:np({method:"POST",fullPath:"/v1/transfers"}),retrieve:np({method:"GET",fullPath:"/v1/transfers/{transfer}"}),update:np({method:"POST",fullPath:"/v1/transfers/{transfer}"}),list:np({method:"GET",fullPath:"/v1/transfers",methodType:"list"}),createReversal:np({method:"POST",fullPath:"/v1/transfers/{id}/reversals"}),listReversals:np({method:"GET",fullPath:"/v1/transfers/{id}/reversals",methodType:"list"}),retrieveReversal:np({method:"GET",fullPath:"/v1/transfers/{transfer}/reversals/{id}"}),updateReversal:np({method:"POST",fullPath:"/v1/transfers/{transfer}/reversals/{id}"})}),nf=em.method,ny=em.extend({create:nf({method:"POST",fullPath:"/v1/webhook_endpoints"}),retrieve:nf({method:"GET",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),update:nf({method:"POST",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),list:nf({method:"GET",fullPath:"/v1/webhook_endpoints",methodType:"list"}),del:nf({method:"DELETE",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"})}),nv=ey("apps",{Secrets:rr}),nP=ey("billing",{Alerts:eE,CreditBalanceSummary:ez,CreditBalanceTransactions:eW,CreditGrants:eV,MeterEventAdjustments:tc,MeterEvents:tP,Meters:tE}),ng=ey("billingPortal",{Configurations:eN,Sessions:rn}),nT=ey("checkout",{Sessions:ra}),n_=ey("climate",{Orders:tb,Products:tU,Suppliers:rh}),nE=ey("entitlements",{ActiveEntitlements:eT,Features:te}),nS=ey("financialConnections",{Accounts:eP,Sessions:rl,Transactions:rE}),nb=ey("forwarding",{Requests:t9}),nO=ey("identity",{VerificationReports:rk,VerificationSessions:rN}),nx=ey("issuing",{Authorizations:ex,Cardholders:eC,Cards:ek,Disputes:e2,PersonalizationDesigns:tN,PhysicalBundles:tF,Tokens:ry,Transactions:rb}),nw=ey("radar",{EarlyFraudWarnings:e4,ValueListItems:rC,ValueLists:rI}),nA=ey("reporting",{ReportRuns:t4,ReportTypes:t6}),nR=ey("sigma",{ScheduledQueryRuns:re}),nC=ey("tax",{Calculations:eA,Registrations:t2,Settings:rc,Transactions:rx}),nG=ey("terminal",{Configurations:eF,ConnectionTokens:eH,Locations:tl,Readers:tz}),nI=ey("testHelpers",{ConfirmationTokens:eU,Customers:eY,Refunds:t1,TestClocks:rm,Issuing:ey("issuing",{Authorizations:eb,Cards:eI,PersonalizationDesigns:tk,Transactions:rT}),Terminal:ey("terminal",{Readers:tH}),Treasury:ey("treasury",{InboundTransfers:tn,OutboundPayments:tx,OutboundTransfers:tC,ReceivedCredits:tW,ReceivedDebits:tQ})}),nj=ey("treasury",{CreditReversals:eQ,DebitReversals:e1,FinancialAccounts:tr,InboundTransfers:ta,OutboundPayments:tA,OutboundTransfers:tI,ReceivedCredits:tV,ReceivedDebits:tY,TransactionEntries:rP,Transactions:rA}),nk=ey("v2",{Billing:ey("billing",{MeterEventAdjustments:th,MeterEventSession:tm,MeterEventStream:ty,MeterEvents:tT}),Core:ey("core",{EventDestinations:e6,Events:e9})}),nD="api.stripe.com",nN="/v1/",nM="2025-05-28.basil",nF=["name","version","url","partner_id"],nq=["authenticator","apiVersion","typescript","maxNetworkRetries","httpAgent","httpClient","timeout","host","port","protocol","telemetry","appInfo","stripeAccount","stripeContext"],nU=e=>new ea(e,em.MAX_BUFFERED_REQUEST_METRICS),nL=function(e,t=nU){function r(n,i={}){if(!(this instanceof r))return new r(n,i);let a=this._getPropsFromConfig(i);this._platformFunctions=e,Object.defineProperty(this,"_emitter",{value:this._platformFunctions.createEmitter(),enumerable:!1,configurable:!1,writable:!1}),this.VERSION=r.PACKAGE_VERSION,this.on=this._emitter.on.bind(this._emitter),this.once=this._emitter.once.bind(this._emitter),this.off=this._emitter.removeListener.bind(this._emitter);let s=a.httpAgent||null;this._api={host:a.host||nD,port:a.port||"443",protocol:a.protocol||"https",basePath:nN,version:a.apiVersion||nM,timeout:I("timeout",a.timeout,8e4),maxNetworkRetries:I("maxNetworkRetries",a.maxNetworkRetries,2),agent:s,httpClient:a.httpClient||(s?this._platformFunctions.createNodeHttpClient(s):this._platformFunctions.createDefaultHttpClient()),dev:!1,stripeAccount:a.stripeAccount||null,stripeContext:a.stripeContext||null};let l=a.typescript||!1;l!==r.USER_AGENT.typescript&&(r.USER_AGENT.typescript=l),a.appInfo&&this._setAppInfo(a.appInfo),this._prepResources(),this._setAuthenticator(n,a.authenticator),this.errors=o,this.webhooks=r.webhooks,this._prevRequestMetrics=[],this._enableTelemetry=!1!==a.telemetry,this._requestSender=t(this),this.StripeResource=r.StripeResource}return r.PACKAGE_VERSION="18.2.1",r.USER_AGENT=Object.assign({bindings_version:r.PACKAGE_VERSION,lang:"node",publisher:"stripe",uname:null,typescript:!1},"undefined"==typeof process?{}:{lang_version:process.version,platform:process.platform}),r.StripeResource=em,r.resources=n,r.HttpClient=m,r.HttpClientResponse=f,r.CryptoProvider=s,r.webhooks=function(e){let t={DEFAULT_TOLERANCE:300,signature:null,constructEvent(e,r,o,n,i,a){try{if(!this.signature)throw Error("ERR: missing signature helper, unable to verify");this.signature.verifyHeader(e,r,o,n||t.DEFAULT_TOLERANCE,i,a)}catch(e){throw e instanceof l&&(e.message+="\nUse `await constructEventAsync(...)` instead of `constructEvent(...)`"),e}return e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},async constructEventAsync(e,r,o,n,i,a){if(!this.signature)throw Error("ERR: missing signature helper, unable to verify");return await this.signature.verifyHeaderAsync(e,r,o,n||t.DEFAULT_TOLERANCE,i,a),e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},generateTestHeaderString:function(e){let t=u(e),r=t.signature||t.cryptoProvider.computeHMACSignature(t.payloadString,t.secret);return t.generateHeaderString(r)},generateTestHeaderStringAsync:async function(e){let t=u(e),r=t.signature||await t.cryptoProvider.computeHMACSignatureAsync(t.payloadString,t.secret);return t.generateHeaderString(r)}},r={EXPECTED_SCHEME:"v1",verifyHeader(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),m=/\s/.test(r),f=(l=l||s()).computeHMACSignature(o(d,h),r);return i(d,c,h,f,a,p,m,u),!0},async verifyHeaderAsync(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),m=/\s/.test(r);l=l||s();let f=await l.computeHMACSignatureAsync(o(d,h),r);return i(d,c,h,f,a,p,m,u)}};function o(e,t){return`${t.timestamp}.${e}`}function n(e,t,r){var o,n;if(!e)throw new Y(t,e,{message:"No webhook payload was provided."});let i="string"!=typeof e&&!(e instanceof Uint8Array),a=new TextDecoder("utf8"),s=e instanceof Uint8Array?a.decode(e):e;if(Array.isArray(t))throw Error("Unexpected: An array was passed as a header, which should not be possible for the stripe-signature header.");if(null==t||""==t)throw new Y(t,e,{message:"No stripe-signature header value was provided."});let l=t instanceof Uint8Array?a.decode(t):t,u=(o=l,n=r,"string"!=typeof o?null:o.split(",").reduce((e,t)=>{let r=t.split("=");return"t"===r[0]&&(e.timestamp=parseInt(r[1],10)),r[0]===n&&e.signatures.push(r[1]),e},{timestamp:-1,signatures:[]}));if(!u||-1===u.timestamp)throw new Y(l,s,{message:"Unable to extract timestamp and signatures from header"});if(!u.signatures.length)throw new Y(l,s,{message:"No signatures found with expected scheme"});return{decodedPayload:s,decodedHeader:l,details:u,suspectPayloadType:i}}function i(t,r,o,n,i,a,s,l){let u=!!o.signatures.filter(e.secureCompare.bind(e,n)).length,c="\nLearn more about webhook signing and explore webhook integration examples for various frameworks at https://docs.stripe.com/webhooks/signature",d=s?"\n\nNote: The provided signing secret contains whitespace. This often indicates an extra newline or space is in the value":"";if(!u){if(a)throw new Y(r,t,{message:"Webhook payload must be provided as a string or a Buffer (https://nodejs.org/api/buffer.html) instance representing the _raw_ request body.Payload was provided as a parsed JavaScript object instead. \nSignature verification is impossible without access to the original signed material. \n"+c+"\n"+d});throw new Y(r,t,{message:"No signatures found matching the expected signature for payload. Are you passing the raw request body you received from Stripe? \n If a webhook request is being forwarded by a third-party tool, ensure that the exact request body, including JSON formatting and new line style, is preserved.\n"+c+"\n"+d})}let h=Math.floor(("number"==typeof l?l:Date.now())/1e3)-o.timestamp;if(i>0&&h>i)throw new Y(r,t,{message:"Timestamp outside the tolerance zone"});return!0}let a=null;function s(){return a||(a=e.createDefaultCryptoProvider()),a}function u(e){if(!e)throw new z({message:"Options are required"});let t=Math.floor(e.timestamp)||Math.floor(Date.now()/1e3),o=e.scheme||r.EXPECTED_SCHEME,n=e.cryptoProvider||s(),i=`${t}.${e.payload}`;return Object.assign(Object.assign({},e),{timestamp:t,scheme:o,cryptoProvider:n,payloadString:i,generateHeaderString:e=>`t=${t},${o}=${e}`})}return t.signature=r,t}(e),r.errors=o,r.createNodeHttpClient=e.createNodeHttpClient,r.createFetchHttpClient=e.createFetchHttpClient,r.createNodeCryptoProvider=e.createNodeCryptoProvider,r.createSubtleCryptoProvider=e.createSubtleCryptoProvider,r.prototype={_appInfo:void 0,on:null,off:null,once:null,VERSION:null,StripeResource:null,webhooks:null,errors:null,_api:null,_prevRequestMetrics:null,_emitter:null,_enableTelemetry:null,_requestSender:null,_platformFunctions:null,rawRequest(e,t,r,o){return this._requestSender._rawRequest(e,t,r,o)},_setAuthenticator(e,t){if(e&&t)throw Error("Can't specify both apiKey and authenticator");if(!e&&!t)throw Error("Neither apiKey nor config.authenticator provided");this._authenticator=e?j(e):t},_setAppInfo(e){if(e&&"object"!=typeof e)throw Error("AppInfo must be an object.");if(e&&!e.name)throw Error("AppInfo.name is required");e=e||{},this._appInfo=nF.reduce((t,r)=>("string"==typeof e[r]&&((t=t||{})[r]=e[r]),t),{})},_setApiField(e,t){this._api[e]=t},getApiField(e){return this._api[e]},setClientId(e){this._clientId=e},getClientId(){return this._clientId},getConstant:e=>{switch(e){case"DEFAULT_HOST":return nD;case"DEFAULT_PORT":return"443";case"DEFAULT_BASE_PATH":return nN;case"DEFAULT_API_VERSION":return nM;case"DEFAULT_TIMEOUT":return 8e4;case"MAX_NETWORK_RETRY_DELAY_SEC":return 5;case"INITIAL_NETWORK_RETRY_DELAY_SEC":return .5}return r[e]},getMaxNetworkRetries(){return this.getApiField("maxNetworkRetries")},_setApiNumberField(e,t,r){let o=I(e,t,r);this._setApiField(e,o)},getMaxNetworkRetryDelay:()=>5,getInitialNetworkRetryDelay:()=>.5,getClientUserAgent(e){return this.getClientUserAgentSeeded(r.USER_AGENT,e)},getClientUserAgentSeeded(e,t){this._platformFunctions.getUname().then(r=>{var o;let n={};for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=encodeURIComponent(null!=(o=e[t])?o:"null"));n.uname=encodeURIComponent(r||"UNKNOWN");let i=this.getApiField("httpClient");i&&(n.httplib=encodeURIComponent(i.getClientName())),this._appInfo&&(n.application=this._appInfo),t(JSON.stringify(n))})},getAppInfoAsString(){if(!this._appInfo)return"";let e=this._appInfo.name;return this._appInfo.version&&(e+=`/${this._appInfo.version}`),this._appInfo.url&&(e+=` (${this._appInfo.url})`),e},getTelemetryEnabled(){return this._enableTelemetry},_prepResources(){for(let e in n)Object.prototype.hasOwnProperty.call(n,e)&&(this["OAuth"===e?"oauth":e[0].toLowerCase()+e.substring(1)]=new n[e](this))},_getPropsFromConfig(e){if(!e)return{};let t="string"==typeof e;if(!(e===Object(e)&&!Array.isArray(e))&&!t)throw Error("Config must either be an object or a string");if(t)return{apiVersion:e};if(Object.keys(e).filter(e=>!nq.includes(e)).length>0)throw Error(`Config object may only contain the following: ${nq.join(", ")}`);return e},parseThinEvent(e,t,r,o,n,i){return this.webhooks.constructEvent(e,t,r,o,n,i)}},r}(new ei)},98363:e=>{"use strict";e.exports=Object}};