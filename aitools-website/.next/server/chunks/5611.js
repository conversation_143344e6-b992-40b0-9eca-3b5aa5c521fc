exports.id=5611,exports.ids=[5611],exports.modules={2328:(e,s,t)=>{"use strict";t.d(s,{LikeProvider:()=>n,X:()=>c});var r=t(60687),l=t(43210),i=t(82136);let a={liked:!1,likes:0,loading:!1},o=(0,l.createContext)(null);function n({children:e}){let{data:s}=(0,i.useSession)(),[t,n]=(0,l.useState)({}),c=(0,l.useCallback)(e=>t[e]||a,[t]),d=(0,l.useCallback)((e,s,t=!1)=>{n(r=>r[e]?r:{...r,[e]:{liked:t,likes:s,loading:!1}})},[]),m=(0,l.useCallback)(async e=>{if(s)try{let s=await fetch(`/api/tools/${e}/like`);if(s.ok){let t=await s.json();t.success&&n(s=>({...s,[e]:{liked:t.data.liked,likes:t.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[s]),x=(0,l.useCallback)(async(e,t=!1)=>{if(!s)return!1;n(s=>({...s,[e]:{...s[e]||a,loading:!0}}));try{let s=await fetch(`/api/tools/${e}/like`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t?{forceUnlike:!0}:{})});if(s.ok){let t=await s.json();if(t.success)return n(s=>({...s,[e]:{liked:t.data.liked,likes:t.data.likes,loading:!1}})),!0}return n(s=>({...s,[e]:{...s[e]||a,loading:!1}})),!1}catch(s){return console.error("Like request failed:",s),n(s=>({...s,[e]:{...s[e]||a,loading:!1}})),!1}},[s]);return(0,r.jsx)(o.Provider,{value:{toolStates:t,toggleLike:x,getToolState:c,initializeToolState:d,refreshToolState:m},children:e})}function c(){let e=(0,l.useContext)(o);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},14008:(e,s,t)=>{"use strict";t.d(s,{default:()=>h});var r=t(60687),l=t(43210),i=t(85814),a=t.n(i),o=t(16189),n=t(82136),c=t(23877),d=t(48577);function m(){let{data:e,status:s}=(0,n.useSession)(),t=(0,o.useRouter)(),[i,a]=(0,l.useState)(!1),[m,x]=(0,l.useState)(!1),u=async()=>{await (0,n.signOut)({callbackUrl:"/"})},h=e=>{x(!1),t.push(e)};return"loading"===s?(0,r.jsx)("button",{className:"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse",disabled:!0,children:"加载中..."}):e?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors",onClick:()=>x(!m),children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:e.user?.image?(0,r.jsx)("img",{src:e.user.image,alt:e.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:e.user?.name?.charAt(0)||"U"})}),(0,r.jsx)("span",{className:"text-sm hidden md:block",children:e.user?.name}),(0,r.jsx)(c.Vr3,{className:`text-xs transition-transform ${m?"rotate-180":""}`})]}),m&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>x(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20",children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:e.user?.image?(0,r.jsx)("img",{src:e.user.image,alt:e.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-lg font-medium text-gray-600",children:e.user?.name?.charAt(0)||"U"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.user?.name}),(0,r.jsx)("p",{className:"text-gray-500 text-xs",children:e.user?.email}),e.user?.role==="admin"&&(0,r.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:"管理员"})]})]})}),(0,r.jsxs)("div",{className:"py-2",children:[(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/profile"),children:[(0,r.jsx)(c.x$1,{}),"个人资料"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/profile/submitted"),children:[(0,r.jsx)(c.svy,{}),"我提交的工具"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/profile/liked"),children:[(0,r.jsx)(c.Mbv,{}),"我的收藏"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/submit"),children:[(0,r.jsx)(c.OiG,{}),"提交工具"]})]}),e.user?.role==="admin"&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"border-t py-2",children:(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/admin"),children:[(0,r.jsx)(c.Pcn,{}),"管理后台"]})})}),(0,r.jsxs)("div",{className:"border-t py-2",children:[(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/settings"),children:[(0,r.jsx)(c.Pcn,{}),"设置"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",onClick:u,children:[(0,r.jsx)(c.axc,{}),"退出登录"]})]})]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors",onClick:()=>a(!0),children:[(0,r.jsx)(c.Zu,{}),"登录"]}),(0,r.jsx)(d.A,{isOpen:i,onClose:()=>a(!1)})]})}let x=({children:e,href:s})=>(0,r.jsx)(a(),{href:s,className:"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors",children:e}),u=[{name:"首页",href:"/"},{name:"工具目录",href:"/tools"},{name:"分类",href:"/categories"},{name:"提交工具",href:"/submit"}];function h(){let[e,s]=(0,l.useState)(!1),t=(0,o.useRouter)(),{data:i}=(0,n.useSession)(),d=e=>{e.preventDefault();let s=new FormData(e.currentTarget).get("search");s.trim()&&t.push(`/search?q=${encodeURIComponent(s.trim())}`)};return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("header",{className:"bg-white px-4 shadow-sm border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,r.jsxs)(a(),{href:"/",className:"flex items-center space-x-2 hover:no-underline",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsxs)("nav",{className:"hidden md:flex space-x-4",children:[u.map(e=>(0,r.jsx)(x,{href:e.href,children:e.name},e.name)),i?.user&&"admin"===i.user.role&&(0,r.jsx)(x,{href:"/admin",children:"管理"})]})]}),(0,r.jsx)("div",{className:"flex-1 max-w-md mx-8 hidden md:block",children:(0,r.jsx)("form",{onSubmit:d,children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(c.KSO,{className:"text-gray-400"})}),(0,r.jsx)("input",{name:"search",type:"text",placeholder:"搜索 AI 工具...",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m,{}),(0,r.jsx)("button",{className:"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",onClick:()=>s(!e),"aria-label":"Open Menu",children:e?(0,r.jsx)(c.QCr,{}):(0,r.jsx)(c.OXb,{})})]})]}),e&&(0,r.jsx)("div",{className:"md:hidden pb-4",children:(0,r.jsxs)("nav",{className:"space-y-4",children:[u.map(e=>(0,r.jsx)(x,{href:e.href,children:e.name},e.name)),i?.user&&"admin"===i.user.role&&(0,r.jsx)(x,{href:"/admin",children:"管理"}),(0,r.jsx)("div",{className:"pt-4",children:(0,r.jsx)("form",{onSubmit:d,children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(c.KSO,{className:"text-gray-400"})}),(0,r.jsx)("input",{name:"search",type:"text",placeholder:"搜索 AI 工具...",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})})]})})]})})}},23440:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx","default")},25536:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},29494:(e,s,t)=>{Promise.resolve().then(t.bind(t,14008)),Promise.resolve().then(t.bind(t,76242)),Promise.resolve().then(t.bind(t,2328))},48577:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(60687),l=t(43210),i=t(82136),a=t(23877);function o({isOpen:e,onClose:s}){let[t,o]=(0,l.useState)("method"),[n,c]=(0,l.useState)(""),[d,m]=(0,l.useState)(""),[x,u]=(0,l.useState)(!1),[h,p]=(0,l.useState)(""),b=(e,s="success")=>{let t=document.createElement("div");t.className=`fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${"success"===s?"bg-green-500":"bg-red-500"}`,t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},g=()=>{o("method"),c(""),m(""),p(""),s()},f=async e=>{try{u(!0),await (0,i.signIn)(e,{callbackUrl:"/"})}catch(e){b("登录失败，请稍后重试","error")}finally{u(!1)}},v=async()=>{if(!n)return void p("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n))return void p("请输入有效的邮箱地址");p(""),u(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:n})}),s=await e.json();s.success?(m(s.token),o("code"),b("验证码已发送，请查看您的邮箱")):b(s.error||"发送失败，请稍后重试","error")}catch(e){b("网络错误，请检查网络连接","error")}finally{u(!1)}},y=async e=>{if(6===e.length){u(!0);try{let s=await (0,i.signIn)("email-code",{email:n,code:e,token:d,redirect:!1});s?.ok?(b("登录成功，欢迎回来！"),g()):b(s?.error||"验证码错误","error")}catch(e){b("网络错误，请检查网络连接","error")}finally{u(!1)}}},j=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");t[e].value=s,s&&e<5&&t[e+1]?.focus();let r=Array.from(t).map(e=>e.value).join("");6===r.length&&y(r)};return e?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:g}),(0,r.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===t&&"登录 AI Tools Directory","email"===t&&"邮箱登录","code"===t&&"输入验证码"]}),(0,r.jsx)("button",{onClick:g,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(a.QCr,{})})]}),(0,r.jsxs)("div",{className:"p-6",children:["method"===t&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>f("google"),disabled:x,children:[(0,r.jsx)(a.DSS,{}),"使用 Google 登录"]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>f("github"),disabled:x,children:[(0,r.jsx)(a.hL4,{}),"使用 GitHub 登录"]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>o("email"),children:[(0,r.jsx)(a.maD,{}),"使用邮箱登录"]})]}),"email"===t&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,r.jsx)("input",{type:"email",value:n,onChange:e=>c(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&v(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),h&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:v,disabled:x,children:x?"发送中...":"发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>o("method"),children:"返回"})]})]}),"code"===t&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",n," 的6位验证码"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,r.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,r.jsx)("input",{type:"text",maxLength:1,onChange:s=>j(e,s.target.value),disabled:x,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>o("email"),children:"重新发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>o("method"),children:"返回"})]})]})]})]})]}):null}},50290:(e,s,t)=>{"use strict";t.d(s,{LikeProvider:()=>l});var r=t(12907);let l=(0,r.registerClientReference)(function(){throw Error("Attempted to call LikeProvider() from the server but LikeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx","LikeProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useLike() from the server but useLike is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx","useLike")},61135:()=>{},61984:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},66446:(e,s,t)=>{Promise.resolve().then(t.bind(t,68926)),Promise.resolve().then(t.bind(t,23440)),Promise.resolve().then(t.bind(t,50290))},68926:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx","default")},76242:(e,s,t)=>{"use strict";t.d(s,{default:()=>i});var r=t(60687),l=t(82136);function i({children:e}){return(0,r.jsx)(l.SessionProvider,{children:e})}},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x,metadata:()=>m});var r=t(37413),l=t(2202),i=t.n(l),a=t(64988),o=t.n(a);t(61135);var n=t(23440),c=t(68926),d=t(50290);let m={title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。",keywords:"AI工具,人工智能,AI导航,机器学习工具,深度学习,自动化工具,AI应用",authors:[{name:"AI工具导航团队"}],creator:"AI工具导航",publisher:"AI工具导航",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"zh_CN",url:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",siteName:"AI工具导航",title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"AI工具导航 - 发现最好的人工智能工具"}]},twitter:{card:"summary_large_image",title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。",images:["/og-image.jpg"]},alternates:{canonical:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"},verification:{google:"your-google-verification-code"}};function x({children:e}){return(0,r.jsx)("html",{lang:"zh",children:(0,r.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:(0,r.jsx)(n.default,{children:(0,r.jsxs)(d.LikeProvider,{children:[(0,r.jsx)(c.default,{}),(0,r.jsx)("main",{children:e})]})})})})}}};