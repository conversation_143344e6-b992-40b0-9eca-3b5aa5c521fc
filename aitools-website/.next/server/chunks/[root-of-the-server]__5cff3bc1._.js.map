{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env.local'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function dbConnect() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default dbConnect;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts"], "sourcesContent": ["// AI工具分类的统一配置文件\n// 这个文件包含所有分类的完整信息，确保整个应用中的一致性\n\nexport interface CategoryConfig {\n  slug: string;\n  name: string;\n  description: string;\n  icon: string;\n  color: string;\n}\n\nexport interface CategoryOption {\n  value: string;\n  label: string;\n}\n\n// 完整的分类配置\nexport const CATEGORY_CONFIGS: CategoryConfig[] = [\n  {\n    slug: 'text-generation',\n    name: '文本生成',\n    description: '利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等',\n    icon: '📝',\n    color: '#3B82F6'\n  },\n  {\n    slug: 'image-generation',\n    name: '图像生成',\n    description: '使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等',\n    icon: '🎨',\n    color: '#10B981'\n  },\n  {\n    slug: 'code-generation',\n    name: '代码生成',\n    description: '智能代码生成和编程辅助工具，提高开发效率',\n    icon: '💻',\n    color: '#8B5CF6'\n  },\n  {\n    slug: 'data-analysis',\n    name: '数据分析',\n    description: '数据分析和可视化工具，帮助洞察数据价值',\n    icon: '📊',\n    color: '#F59E0B'\n  },\n  {\n    slug: 'audio-processing',\n    name: '音频处理',\n    description: '音频处理、语音合成、音乐生成等音频AI工具',\n    icon: '🎵',\n    color: '#EF4444'\n  },\n  {\n    slug: 'video-editing',\n    name: '视频编辑',\n    description: '视频生成、编辑、剪辑等视频处理AI工具',\n    icon: '🎬',\n    color: '#06B6D4'\n  },\n  {\n    slug: 'translation',\n    name: '语言翻译',\n    description: '多语言翻译和本地化AI工具',\n    icon: '🌐',\n    color: '#84CC16'\n  },\n  {\n    slug: 'search-engines',\n    name: '搜索引擎',\n    description: '智能搜索和信息检索AI工具',\n    icon: '🔍',\n    color: '#F97316'\n  },\n  {\n    slug: 'education',\n    name: '教育学习',\n    description: '教育培训和学习辅助AI工具',\n    icon: '📚',\n    color: '#A855F7'\n  },\n  {\n    slug: 'marketing',\n    name: '营销工具',\n    description: '数字营销和推广AI工具',\n    icon: '📈',\n    color: '#EC4899'\n  },\n  {\n    slug: 'productivity',\n    name: '生产力工具',\n    description: '提高工作效率的AI工具',\n    icon: '⚡',\n    color: '#14B8A6'\n  },\n  {\n    slug: 'customer-service',\n    name: '客户服务',\n    description: '客户支持和服务AI工具',\n    icon: '🎧',\n    color: '#F59E0B'\n  }\n];\n\n// 生成分类选项（用于下拉框等）\nexport const CATEGORY_OPTIONS: CategoryOption[] = CATEGORY_CONFIGS.map(config => ({\n  value: config.slug,\n  label: config.name\n}));\n\n// 包含\"所有分类\"选项的分类选项\nexport const CATEGORY_OPTIONS_WITH_ALL: CategoryOption[] = [\n  { value: '', label: '所有分类' },\n  ...CATEGORY_OPTIONS\n];\n\n// 分类标签映射（slug -> 名称）\nexport const CATEGORY_LABELS: Record<string, string> = CATEGORY_CONFIGS.reduce(\n  (acc, config) => {\n    acc[config.slug] = config.name;\n    return acc;\n  },\n  {} as Record<string, string>\n);\n\n// 分类元数据映射（slug -> 完整配置）\nexport const CATEGORY_METADATA: Record<string, CategoryConfig> = CATEGORY_CONFIGS.reduce(\n  (acc, config) => {\n    acc[config.slug] = config;\n    return acc;\n  },\n  {} as Record<string, CategoryConfig>\n);\n\n// 获取分类配置的辅助函数\nexport const getCategoryConfig = (slug: string): CategoryConfig | undefined => {\n  return CATEGORY_METADATA[slug];\n};\n\n// 获取分类名称的辅助函数\nexport const getCategoryName = (slug: string): string => {\n  return CATEGORY_LABELS[slug] || slug;\n};\n\n// 验证分类是否存在的辅助函数\nexport const isValidCategory = (slug: string): boolean => {\n  return slug in CATEGORY_METADATA;\n};\n\n// 获取所有分类slug的数组\nexport const CATEGORY_SLUGS = CATEGORY_CONFIGS.map(config => config.slug);\n"], "names": [], "mappings": "AAAA,gBAAgB;AAChB,8BAA8B;;;;;;;;;;;;AAgBvB,MAAM,mBAAqC;IAChD;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;CACD;AAGM,MAAM,mBAAqC,iBAAiB,GAAG,CAAC,CAAA,SAAU,CAAC;QAChF,OAAO,OAAO,IAAI;QAClB,OAAO,OAAO,IAAI;IACpB,CAAC;AAGM,MAAM,4BAA8C;IACzD;QAAE,OAAO;QAAI,OAAO;IAAO;OACxB;CACJ;AAGM,MAAM,kBAA0C,iBAAiB,MAAM,CAC5E,CAAC,KAAK;IACJ,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI;IAC9B,OAAO;AACT,GACA,CAAC;AAII,MAAM,oBAAoD,iBAAiB,MAAM,CACtF,CAAC,KAAK;IACJ,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG;IACnB,OAAO;AACT,GACA,CAAC;AAII,MAAM,oBAAoB,CAAC;IAChC,OAAO,iBAAiB,CAAC,KAAK;AAChC;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,eAAe,CAAC,KAAK,IAAI;AAClC;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,QAAQ;AACjB;AAGO,MAAM,iBAAiB,iBAAiB,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { CATEGORY_SLUGS } from '@/constants/categories';\n\nexport interface ITool extends Document {\n  name: string;\n  tagline?: string; // 工具标语/副标题\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string; // User ID who submitted\n  submittedAt: Date;\n  launchDate?: Date; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string; // Admin ID who reviewed\n  reviewedAt?: Date;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean; // 是否已选择发布日期\n  selectedLaunchDate?: Date; // 用户选择的发布日期\n  launchOption?: 'free' | 'paid'; // 发布选项：免费或付费\n\n  // 付费相关\n  paymentRequired?: boolean; // 是否需要付费\n  paymentAmount?: number; // 付费金额（分为单位）\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded'; // 支付状态\n  orderId?: string; // 订单ID\n  paymentMethod?: string; // 支付方式\n  paidAt?: Date; // 支付完成时间\n\n  views: number;\n  likes: number;\n  likedBy: string[]; // 点赞用户ID列表\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst ToolSchema: Schema = new Schema({\n  name: {\n    type: String,\n    required: [true, 'Tool name is required'],\n    trim: true,\n    maxlength: [100, 'Tool name cannot exceed 100 characters']\n  },\n  tagline: {\n    type: String,\n    trim: true,\n    maxlength: [200, 'Tagline cannot exceed 200 characters']\n  },\n  description: {\n    type: String,\n    required: [true, 'Tool description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot exceed 500 characters']\n  },\n  longDescription: {\n    type: String,\n    trim: true,\n    maxlength: [2000, 'Long description cannot exceed 2000 characters']\n  },\n  website: {\n    type: String,\n    required: [true, 'Website URL is required'],\n    trim: true,\n    validate: {\n      validator: function(v: string) {\n        return /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'Please enter a valid URL'\n    }\n  },\n  logo: {\n    type: String,\n    trim: true\n  },\n  category: {\n    type: String,\n    required: [true, 'Category is required'],\n    enum: CATEGORY_SLUGS\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    lowercase: true\n  }],\n  pricing: {\n    type: String,\n    required: [true, 'Pricing model is required'],\n    enum: ['free', 'freemium', 'paid']\n  },\n  pricingDetails: {\n    type: String,\n    trim: true,\n    maxlength: [500, 'Pricing details cannot exceed 500 characters']\n  },\n  screenshots: [{\n    type: String,\n    trim: true\n  }],\n  submittedBy: {\n    type: String,\n    required: [true, 'Submitter ID is required'],\n    trim: true\n  },\n  submittedAt: {\n    type: Date,\n    default: Date.now\n  },\n  launchDate: {\n    type: Date\n  },\n  status: {\n    type: String,\n    required: true,\n    enum: ['draft', 'pending', 'approved', 'rejected'],\n    default: 'draft'\n  },\n  reviewNotes: {\n    type: String,\n    trim: true,\n    maxlength: [1000, 'Review notes cannot exceed 1000 characters']\n  },\n  reviewedBy: {\n    type: String,\n    trim: true\n  },\n  reviewedAt: {\n    type: Date\n  },\n\n  // 发布日期选择相关\n  launchDateSelected: {\n    type: Boolean,\n    default: false\n  },\n  selectedLaunchDate: {\n    type: Date\n  },\n  launchOption: {\n    type: String,\n    enum: ['free', 'paid']\n  },\n\n  // 付费相关\n  paymentRequired: {\n    type: Boolean,\n    default: false\n  },\n  paymentAmount: {\n    type: Number,\n    min: 0\n  },\n  paymentStatus: {\n    type: String,\n    enum: ['pending', 'completed', 'failed', 'refunded']\n  },\n  orderId: {\n    type: String,\n    trim: true\n  },\n  paymentMethod: {\n    type: String,\n    trim: true\n  },\n  paidAt: {\n    type: Date\n  },\n  views: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likes: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likedBy: [{\n    type: String,\n    trim: true\n  }],\n  isActive: {\n    type: Boolean,\n    default: true\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes for better query performance\nToolSchema.index({ status: 1, isActive: 1 });\nToolSchema.index({ category: 1, status: 1 });\nToolSchema.index({ tags: 1, status: 1 });\nToolSchema.index({ submittedBy: 1 });\nToolSchema.index({ launchDate: -1 });\nToolSchema.index({ views: -1 });\nToolSchema.index({ likes: -1 });\n\n// Text search index\nToolSchema.index({\n  name: 'text',\n  tagline: 'text',\n  description: 'text',\n  longDescription: 'text',\n  tags: 'text'\n});\n\nexport default mongoose.models.Tool || mongoose.model<ITool>('Tool', ToolSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA2CA,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;QACN,WAAW;YAAC;YAAK;SAAyC;IAC5D;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAAuC;IAC1D;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAAiD;IACrE;IACA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM,gIAAA,CAAA,iBAAc;IACtB;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,MAAM;YAAC;YAAQ;YAAY;SAAO;IACpC;IACA,gBAAgB;QACd,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAA+C;IAClE;IACA,aAAa;QAAC;YACZ,MAAM;YACN,MAAM;QACR;KAAE;IACF,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,YAAY;QACV,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAS;YAAW;YAAY;SAAW;QAClD,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAA6C;IACjE;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;IACR;IAEA,WAAW;IACX,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;YAAC;YAAQ;SAAO;IACxB;IAEA,OAAO;IACP,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,KAAK;IACP;IACA,eAAe;QACb,MAAM;QACN,MAAM;YAAC;YAAW;YAAa;YAAU;SAAW;IACtD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,eAAe;QACb,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,SAAS;QAAC;YACR,MAAM;YACN,MAAM;QACR;KAAE;IACF,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,uCAAuC;AACvC,WAAW,KAAK,CAAC;IAAE,QAAQ;IAAG,UAAU;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,QAAQ;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,MAAM;IAAG,QAAQ;AAAE;AACtC,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,YAAY,CAAC;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAE7B,oBAAoB;AACpB,WAAW,KAAK,CAAC;IACf,MAAM;IACN,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,MAAM;AACR;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport dbConnect from '@/lib/mongodb';\nimport Tool from '@/models/Tool';\n\n// GET /api/admin/stats - 获取管理员统计数据\nexport async function GET(request: NextRequest) {\n  try {\n    await dbConnect();\n\n    const { searchParams } = new URL(request.url);\n    const timeRange = searchParams.get('timeRange') || '7d';\n\n    // 计算时间范围\n    const now = new Date();\n    let startDate: Date;\n    \n    switch (timeRange) {\n      case '1d':\n        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n        break;\n      case '7d':\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        break;\n      case '30d':\n        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n        break;\n      case '90d':\n        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);\n        break;\n      default:\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n    }\n\n    // 基础统计\n    const [\n      totalTools,\n      pendingTools,\n      approvedTools,\n      rejectedTools,\n      totalViews,\n      totalLikes,\n      recentSubmissions,\n      recentApprovals,\n      recentRejections\n    ] = await Promise.all([\n      Tool.countDocuments(),\n      Tool.countDocuments({ status: 'pending' }),\n      Tool.countDocuments({ status: 'approved' }),\n      Tool.countDocuments({ status: 'rejected' }),\n      Tool.aggregate([{ $group: { _id: null, total: { $sum: '$views' } } }]),\n      Tool.aggregate([{ $group: { _id: null, total: { $sum: '$likes' } } }]),\n      Tool.countDocuments({ submittedAt: { $gte: startDate } }),\n      Tool.countDocuments({ \n        status: 'approved', \n        reviewedAt: { $gte: startDate } \n      }),\n      Tool.countDocuments({ \n        status: 'rejected', \n        reviewedAt: { $gte: startDate } \n      })\n    ]);\n\n    // 分类统计\n    const categoryStats = await Tool.aggregate([\n      { $match: { status: 'approved' } },\n      {\n        $group: {\n          _id: '$category',\n          count: { $sum: 1 },\n          totalViews: { $sum: '$views' },\n          totalLikes: { $sum: '$likes' }\n        }\n      },\n      { $sort: { count: -1 } }\n    ]);\n\n    // 热门工具\n    const topTools = await Tool.find({ status: 'approved' })\n      .sort({ views: -1 })\n      .limit(10)\n      .select('name category views likes')\n      .lean();\n\n    // 最近活动\n    const recentActivity = await Tool.find({\n      $or: [\n        { submittedAt: { $gte: startDate } },\n        { reviewedAt: { $gte: startDate } }\n      ]\n    })\n    .sort({ updatedAt: -1 })\n    .limit(20)\n    .select('name status submittedAt reviewedAt submittedBy reviewedBy')\n    .lean();\n\n    // 每日统计（最近7天）\n    const dailyStats = await Tool.aggregate([\n      {\n        $match: {\n          submittedAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) }\n        }\n      },\n      {\n        $group: {\n          _id: {\n            date: { $dateToString: { format: '%Y-%m-%d', date: '$submittedAt' } },\n            status: '$status'\n          },\n          count: { $sum: 1 }\n        }\n      },\n      { $sort: { '_id.date': 1 } }\n    ]);\n\n    // 处理每日统计数据\n    const last7Days = [];\n    for (let i = 6; i >= 0; i--) {\n      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);\n      const dateStr = date.toISOString().split('T')[0];\n      const dayName = date.toLocaleDateString('zh-CN', { weekday: 'short' });\n      \n      const dayStats = dailyStats.filter(stat => stat._id.date === dateStr);\n      const submissions = dayStats.find(s => s._id.status === 'pending')?.count || 0;\n      const approvals = dayStats.find(s => s._id.status === 'approved')?.count || 0;\n      const rejections = dayStats.find(s => s._id.status === 'rejected')?.count || 0;\n      \n      last7Days.push({\n        date: dateStr,\n        day: dayName,\n        submissions,\n        approvals,\n        rejections\n      });\n    }\n\n    // 审核效率统计\n    const avgReviewTime = await Tool.aggregate([\n      {\n        $match: {\n          status: { $in: ['approved', 'rejected'] },\n          reviewedAt: { $exists: true },\n          submittedAt: { $exists: true }\n        }\n      },\n      {\n        $project: {\n          reviewTime: {\n            $divide: [\n              { $subtract: ['$reviewedAt', '$submittedAt'] },\n              1000 * 60 * 60 // 转换为小时\n            ]\n          }\n        }\n      },\n      {\n        $group: {\n          _id: null,\n          avgReviewTime: { $avg: '$reviewTime' }\n        }\n      }\n    ]);\n\n    const overview = {\n      totalTools,\n      pendingTools,\n      approvedTools,\n      rejectedTools,\n      totalViews: totalViews[0]?.total || 0,\n      totalLikes: totalLikes[0]?.total || 0,\n      recentSubmissions,\n      recentApprovals,\n      recentRejections,\n      avgReviewTime: avgReviewTime[0]?.avgReviewTime || 0\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        overview,\n        categoryStats,\n        topTools,\n        recentActivity,\n        dailyStats: last7Days,\n        timeRange\n      }\n    });\n\n  } catch (error) {\n    console.error('Error fetching admin stats:', error);\n    return NextResponse.json(\n      { success: false, error: '获取统计数据失败' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QAEnD,SAAS;QACT,MAAM,MAAM,IAAI;QAChB,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK;gBACpD;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;gBACxD;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;gBACzD;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;gBACzD;YACF;gBACE,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;QAC5D;QAEA,OAAO;QACP,MAAM,CACJ,YACA,cACA,eACA,eACA,YACA,YACA,mBACA,iBACA,iBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,uHAAA,CAAA,UAAI,CAAC,cAAc;YACnB,uHAAA,CAAA,UAAI,CAAC,cAAc,CAAC;gBAAE,QAAQ;YAAU;YACxC,uHAAA,CAAA,UAAI,CAAC,cAAc,CAAC;gBAAE,QAAQ;YAAW;YACzC,uHAAA,CAAA,UAAI,CAAC,cAAc,CAAC;gBAAE,QAAQ;YAAW;YACzC,uHAAA,CAAA,UAAI,CAAC,SAAS,CAAC;gBAAC;oBAAE,QAAQ;wBAAE,KAAK;wBAAM,OAAO;4BAAE,MAAM;wBAAS;oBAAE;gBAAE;aAAE;YACrE,uHAAA,CAAA,UAAI,CAAC,SAAS,CAAC;gBAAC;oBAAE,QAAQ;wBAAE,KAAK;wBAAM,OAAO;4BAAE,MAAM;wBAAS;oBAAE;gBAAE;aAAE;YACrE,uHAAA,CAAA,UAAI,CAAC,cAAc,CAAC;gBAAE,aAAa;oBAAE,MAAM;gBAAU;YAAE;YACvD,uHAAA,CAAA,UAAI,CAAC,cAAc,CAAC;gBAClB,QAAQ;gBACR,YAAY;oBAAE,MAAM;gBAAU;YAChC;YACA,uHAAA,CAAA,UAAI,CAAC,cAAc,CAAC;gBAClB,QAAQ;gBACR,YAAY;oBAAE,MAAM;gBAAU;YAChC;SACD;QAED,OAAO;QACP,MAAM,gBAAgB,MAAM,uHAAA,CAAA,UAAI,CAAC,SAAS,CAAC;YACzC;gBAAE,QAAQ;oBAAE,QAAQ;gBAAW;YAAE;YACjC;gBACE,QAAQ;oBACN,KAAK;oBACL,OAAO;wBAAE,MAAM;oBAAE;oBACjB,YAAY;wBAAE,MAAM;oBAAS;oBAC7B,YAAY;wBAAE,MAAM;oBAAS;gBAC/B;YACF;YACA;gBAAE,OAAO;oBAAE,OAAO,CAAC;gBAAE;YAAE;SACxB;QAED,OAAO;QACP,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YAAE,QAAQ;QAAW,GACnD,IAAI,CAAC;YAAE,OAAO,CAAC;QAAE,GACjB,KAAK,CAAC,IACN,MAAM,CAAC,6BACP,IAAI;QAEP,OAAO;QACP,MAAM,iBAAiB,MAAM,uHAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YACrC,KAAK;gBACH;oBAAE,aAAa;wBAAE,MAAM;oBAAU;gBAAE;gBACnC;oBAAE,YAAY;wBAAE,MAAM;oBAAU;gBAAE;aACnC;QACH,GACC,IAAI,CAAC;YAAE,WAAW,CAAC;QAAE,GACrB,KAAK,CAAC,IACN,MAAM,CAAC,6DACP,IAAI;QAEL,aAAa;QACb,MAAM,aAAa,MAAM,uHAAA,CAAA,UAAI,CAAC,SAAS,CAAC;YACtC;gBACE,QAAQ;oBACN,aAAa;wBAAE,MAAM,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;oBAAM;gBACzE;YACF;YACA;gBACE,QAAQ;oBACN,KAAK;wBACH,MAAM;4BAAE,eAAe;gCAAE,QAAQ;gCAAY,MAAM;4BAAe;wBAAE;wBACpE,QAAQ;oBACV;oBACA,OAAO;wBAAE,MAAM;oBAAE;gBACnB;YACF;YACA;gBAAE,OAAO;oBAAE,YAAY;gBAAE;YAAE;SAC5B;QAED,WAAW;QACX,MAAM,YAAY,EAAE;QACpB,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;YAC3B,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;YACzD,MAAM,UAAU,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAChD,MAAM,UAAU,KAAK,kBAAkB,CAAC,SAAS;gBAAE,SAAS;YAAQ;YAEpE,MAAM,WAAW,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,CAAC,IAAI,KAAK;YAC7D,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,CAAC,MAAM,KAAK,YAAY,SAAS;YAC7E,MAAM,YAAY,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,CAAC,MAAM,KAAK,aAAa,SAAS;YAC5E,MAAM,aAAa,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,CAAC,MAAM,KAAK,aAAa,SAAS;YAE7E,UAAU,IAAI,CAAC;gBACb,MAAM;gBACN,KAAK;gBACL;gBACA;gBACA;YACF;QACF;QAEA,SAAS;QACT,MAAM,gBAAgB,MAAM,uHAAA,CAAA,UAAI,CAAC,SAAS,CAAC;YACzC;gBACE,QAAQ;oBACN,QAAQ;wBAAE,KAAK;4BAAC;4BAAY;yBAAW;oBAAC;oBACxC,YAAY;wBAAE,SAAS;oBAAK;oBAC5B,aAAa;wBAAE,SAAS;oBAAK;gBAC/B;YACF;YACA;gBACE,UAAU;oBACR,YAAY;wBACV,SAAS;4BACP;gCAAE,WAAW;oCAAC;oCAAe;iCAAe;4BAAC;4BAC7C,OAAO,KAAK,GAAG,QAAQ;yBACxB;oBACH;gBACF;YACF;YACA;gBACE,QAAQ;oBACN,KAAK;oBACL,eAAe;wBAAE,MAAM;oBAAc;gBACvC;YACF;SACD;QAED,MAAM,WAAW;YACf;YACA;YACA;YACA;YACA,YAAY,UAAU,CAAC,EAAE,EAAE,SAAS;YACpC,YAAY,UAAU,CAAC,EAAE,EAAE,SAAS;YACpC;YACA;YACA;YACA,eAAe,aAAa,CAAC,EAAE,EAAE,iBAAiB;QACpD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA,YAAY;gBACZ;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}