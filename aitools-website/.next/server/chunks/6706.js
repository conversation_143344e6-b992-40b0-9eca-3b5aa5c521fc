"use strict";exports.id=6706,exports.ids=[6706],exports.modules={79171:(e,r,t)=>{t.d(r,{kX:()=>a});let a={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}};a.FREE_LAUNCH.description,a.FREE_LAUNCH.displayPrice,a.FREE_LAUNCH.features,a.PRIORITY_LAUNCH.description,a.PRIORITY_LAUNCH.displayPrice,a.PRIORITY_LAUNCH.features;let o={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}};o.FREE.value,o.FREE.label,o.FREEMIUM.value,o.FREEMIUM.label,o.PAID.value,o.PAID.label,o.FREE.value,o.FREE.label,o.FREEMIUM.value,o.FREEMIUM.label,o.PAID.value,o.PAID.label},96706:(e,r,t)=>{t.d(r,{ZW:()=>l,bw:()=>s,f:()=>n,stripe:()=>c});var a=t(97877),o=t(79171);let c=new a.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-12-18.acacia",typescript:!0});async function n(e,r="cny",t={}){try{return await c.paymentIntents.create({amount:e,currency:r,metadata:t,automatic_payment_methods:{enabled:!0}})}catch(e){throw console.error("Error creating payment intent:",e),Error("Failed to create payment intent")}}async function i(e,r,t={}){try{return await c.customers.create({email:e,name:r,metadata:t})}catch(e){throw console.error("Error creating Stripe customer:",e),Error("Failed to create customer")}}async function s(e,r,t={}){try{let a=await c.customers.list({email:e,limit:1});if(a.data.length>0)return a.data[0];return await i(e,r,t)}catch(e){throw console.error("Error getting or creating Stripe customer:",e),Error("Failed to get or create customer")}}function l(e,r,t){try{return c.webhooks.constructEvent(e,r,t)}catch(e){throw console.error("Error constructing webhook event:",e),Error("Invalid webhook signature")}}o.kX.PRIORITY_LAUNCH.productName,o.kX.PRIORITY_LAUNCH.stripeAmount,o.kX.PRIORITY_LAUNCH.stripeCurrency,o.kX.PRIORITY_LAUNCH.description,o.kX.PRIORITY_LAUNCH.features}};