[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts": "54", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts": "55", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts": "56", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts": "57", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts": "58", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts": "59", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts": "60", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts": "61", "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx": "62", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx": "63", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx": "64", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx": "65", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx": "66", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx": "67", "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx": "68", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts": "69", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts": "70", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx": "71", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts": "72", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts": "73", "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts": "74", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-pricing/page.tsx": "75", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx": "76", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx": "77", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx": "78", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx": "79", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx": "80", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts": "81", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx": "82", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts": "83", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx": "84", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx": "85", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx": "86", "/Users/<USER>/workspace/aitools/aitools-website/src/app/about/page.tsx": "87", "/Users/<USER>/workspace/aitools/aitools-website/src/app/contact/page.tsx": "88", "/Users/<USER>/workspace/aitools/aitools-website/src/app/privacy/page.tsx": "89", "/Users/<USER>/workspace/aitools/aitools-website/src/app/terms/page.tsx": "90", "/Users/<USER>/workspace/aitools/aitools-website/src/components/admin/AdminLayout.tsx": "91", "/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx": "92", "/Users/<USER>/workspace/aitools/aitools-website/src/middleware.ts": "93"}, {"size": 12788, "mtime": 1751090029881, "results": "94", "hashOfConfig": "95"}, {"size": 17369, "mtime": 1751089864348, "results": "96", "hashOfConfig": "95"}, {"size": 15702, "mtime": 1751039524674, "results": "97", "hashOfConfig": "95"}, {"size": 5321, "mtime": 1750906802986, "results": "98", "hashOfConfig": "95"}, {"size": 1926, "mtime": 1751014639961, "results": "99", "hashOfConfig": "95"}, {"size": 2073, "mtime": 1751038886395, "results": "100", "hashOfConfig": "95"}, {"size": 3121, "mtime": 1751014815428, "results": "101", "hashOfConfig": "95"}, {"size": 171, "mtime": 1750921851894, "results": "102", "hashOfConfig": "95"}, {"size": 3714, "mtime": 1750921931408, "results": "103", "hashOfConfig": "95"}, {"size": 4489, "mtime": 1750930430193, "results": "104", "hashOfConfig": "95"}, {"size": 2140, "mtime": 1751072700801, "results": "105", "hashOfConfig": "95"}, {"size": 4403, "mtime": 1750924179468, "results": "106", "hashOfConfig": "95"}, {"size": 3797, "mtime": 1751088061800, "results": "107", "hashOfConfig": "95"}, {"size": 7403, "mtime": 1751038610858, "results": "108", "hashOfConfig": "95"}, {"size": 5019, "mtime": 1751038932824, "results": "109", "hashOfConfig": "95"}, {"size": 5538, "mtime": 1750952541605, "results": "110", "hashOfConfig": "95"}, {"size": 2484, "mtime": 1750938669998, "results": "111", "hashOfConfig": "95"}, {"size": 8201, "mtime": 1751080106824, "results": "112", "hashOfConfig": "95"}, {"size": 13916, "mtime": 1751072868169, "results": "113", "hashOfConfig": "95"}, {"size": 2659, "mtime": 1751082448491, "results": "114", "hashOfConfig": "95"}, {"size": 14791, "mtime": 1751081233145, "results": "115", "hashOfConfig": "95"}, {"size": 7667, "mtime": 1751086387926, "results": "116", "hashOfConfig": "95"}, {"size": 10389, "mtime": 1750945315721, "results": "117", "hashOfConfig": "95"}, {"size": 21016, "mtime": 1751039634571, "results": "118", "hashOfConfig": "95"}, {"size": 21004, "mtime": 1750945519465, "results": "119", "hashOfConfig": "95"}, {"size": 15711, "mtime": 1751072831098, "results": "120", "hashOfConfig": "95"}, {"size": 4543, "mtime": 1750930937103, "results": "121", "hashOfConfig": "95"}, {"size": 5154, "mtime": 1751078866193, "results": "122", "hashOfConfig": "95"}, {"size": 5098, "mtime": 1751081125147, "results": "123", "hashOfConfig": "95"}, {"size": 1425, "mtime": 1750903550616, "results": "124", "hashOfConfig": "95"}, {"size": 845, "mtime": 1750908285683, "results": "125", "hashOfConfig": "95"}, {"size": 3162, "mtime": 1751023074001, "results": "126", "hashOfConfig": "95"}, {"size": 505, "mtime": 1750908273441, "results": "127", "hashOfConfig": "95"}, {"size": 863, "mtime": 1750908296528, "results": "128", "hashOfConfig": "95"}, {"size": 5768, "mtime": 1750942157899, "results": "129", "hashOfConfig": "95"}, {"size": 4598, "mtime": 1751084769792, "results": "130", "hashOfConfig": "95"}, {"size": 9109, "mtime": 1750930558601, "results": "131", "hashOfConfig": "95"}, {"size": 6707, "mtime": 1751090920440, "results": "132", "hashOfConfig": "95"}, {"size": 5036, "mtime": 1751090891814, "results": "133", "hashOfConfig": "95"}, {"size": 867, "mtime": 1750922283437, "results": "134", "hashOfConfig": "95"}, {"size": 362, "mtime": 1750922147686, "results": "135", "hashOfConfig": "95"}, {"size": 8935, "mtime": 1750924218629, "results": "136", "hashOfConfig": "95"}, {"size": 3041, "mtime": 1751085104518, "results": "137", "hashOfConfig": "95"}, {"size": 2449, "mtime": 1750942881883, "results": "138", "hashOfConfig": "95"}, {"size": 7116, "mtime": 1751084985951, "results": "139", "hashOfConfig": "95"}, {"size": 5616, "mtime": 1751090804736, "results": "140", "hashOfConfig": "95"}, {"size": 921, "mtime": 1750903252798, "results": "141", "hashOfConfig": "95"}, {"size": 5018, "mtime": 1751073079886, "results": "142", "hashOfConfig": "95"}, {"size": 1667, "mtime": 1750903308052, "results": "143", "hashOfConfig": "95"}, {"size": 2141, "mtime": 1750921803605, "results": "144", "hashOfConfig": "95"}, {"size": 4854, "mtime": 1751073025366, "results": "145", "hashOfConfig": "95"}, {"size": 3406, "mtime": 1751090710634, "results": "146", "hashOfConfig": "95"}, {"size": 720, "mtime": 1750903327281, "results": "147", "hashOfConfig": "95"}, {"size": 3866, "mtime": 1750984404444, "results": "148", "hashOfConfig": "95"}, {"size": 2238, "mtime": 1750995712168, "results": "149", "hashOfConfig": "95"}, {"size": 4057, "mtime": 1751018497440, "results": "150", "hashOfConfig": "95"}, {"size": 5242, "mtime": 1751013821668, "results": "151", "hashOfConfig": "95"}, {"size": 1022, "mtime": 1750984456438, "results": "152", "hashOfConfig": "95"}, {"size": 11976, "mtime": 1751038689677, "results": "153", "hashOfConfig": "95"}, {"size": 2237, "mtime": 1750949131424, "results": "154", "hashOfConfig": "95"}, {"size": 3202, "mtime": 1750953105864, "results": "155", "hashOfConfig": "95"}, {"size": 8048, "mtime": 1751018710311, "results": "156", "hashOfConfig": "95"}, {"size": 6764, "mtime": 1751005731451, "results": "157", "hashOfConfig": "95"}, {"size": 4621, "mtime": 1751005744888, "results": "158", "hashOfConfig": "95"}, {"size": 12431, "mtime": 1751004268664, "results": "159", "hashOfConfig": "95"}, {"size": 3885, "mtime": 1751018851458, "results": "160", "hashOfConfig": "95"}, {"size": 7687, "mtime": 1751017905894, "results": "161", "hashOfConfig": "95"}, {"size": 3527, "mtime": 1751018284048, "results": "162", "hashOfConfig": "95"}, {"size": 3985, "mtime": 1751017840303, "results": "163", "hashOfConfig": "95"}, {"size": 3989, "mtime": 1750984256539, "results": "164", "hashOfConfig": "95"}, {"size": 22918, "mtime": 1751072978974, "results": "165", "hashOfConfig": "95"}, {"size": 3519, "mtime": 1751038552644, "results": "166", "hashOfConfig": "95"}, {"size": 4413, "mtime": 1751019030952, "results": "167", "hashOfConfig": "95"}, {"size": 2916, "mtime": 1751080520878, "results": "168", "hashOfConfig": "95"}, {"size": 6972, "mtime": 1751018762448, "results": "169", "hashOfConfig": "95"}, {"size": 5275, "mtime": 1751023043127, "results": "170", "hashOfConfig": "95"}, {"size": 2605, "mtime": 1751019665417, "results": "171", "hashOfConfig": "95"}, {"size": 9621, "mtime": 1751082329362, "results": "172", "hashOfConfig": "95"}, {"size": 5433, "mtime": 1751023279983, "results": "173", "hashOfConfig": "95"}, {"size": 3048, "mtime": 1751023262678, "results": "174", "hashOfConfig": "95"}, {"size": 3274, "mtime": 1751081422931, "results": "175", "hashOfConfig": "95"}, {"size": 6566, "mtime": 1751081195986, "results": "176", "hashOfConfig": "95"}, {"size": 3849, "mtime": 1751072665492, "results": "177", "hashOfConfig": "95"}, {"size": 8364, "mtime": 1751075897950, "results": "178", "hashOfConfig": "95"}, {"size": 4515, "mtime": 1751076728457, "results": "179", "hashOfConfig": "95"}, {"size": 10787, "mtime": 1751076826290, "results": "180", "hashOfConfig": "95"}, {"size": 8982, "mtime": 1751080369147, "results": "181", "hashOfConfig": "95"}, {"size": 11513, "mtime": 1751080448998, "results": "182", "hashOfConfig": "95"}, {"size": 10482, "mtime": 1751080503293, "results": "183", "hashOfConfig": "95"}, {"size": 11961, "mtime": 1751080591727, "results": "184", "hashOfConfig": "95"}, {"size": 2533, "mtime": 1751090862127, "results": "185", "hashOfConfig": "95"}, {"size": 5354, "mtime": 1751086484747, "results": "186", "hashOfConfig": "95"}, {"size": 776, "mtime": 1751090833335, "results": "187", "hashOfConfig": "95"}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ifueu7", {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx", ["467", "468", "469", "470", "471", "472", "473", "474", "475", "476"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx", ["477", "478", "479", "480", "481", "482"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx", ["483", "484", "485", "486", "487"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["488", "489"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["490", "491"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", ["492"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["493", "494"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["495", "496", "497"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["498"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx", ["499", "500", "501"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx", ["502", "503"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx", ["504"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx", ["505", "506"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx", ["507", "508", "509"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx", ["510"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx", ["511", "512", "513", "514", "515", "516"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx", ["517"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx", ["518", "519", "520", "521"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx", ["522"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx", ["523", "524"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["525", "526", "527"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx", ["528", "529"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["530", "531", "532", "533"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", ["534"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["535"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["536", "537", "538", "539", "540"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["541"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts", ["542"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts", ["543"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts", ["544", "545"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts", ["546", "547", "548", "549"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx", ["550", "551", "552", "553", "554"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx", ["555", "556"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx", ["557", "558"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx", ["559", "560", "561"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx", ["562"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx", ["563"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx", ["564"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-pricing/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx", ["565", "566", "567"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx", ["568", "569", "570", "571", "572"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx", ["573"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx", ["574", "575", "576"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx", ["577", "578"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/about/page.tsx", ["579"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/contact/page.tsx", ["580", "581"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/privacy/page.tsx", ["582"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/terms/page.tsx", ["583", "584", "585", "586", "587", "588", "589"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/admin/AdminLayout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx", ["590"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/middleware.ts", ["591"], [], {"ruleId": "592", "severity": 2, "message": "593", "line": 12, "column": 3, "nodeType": null, "messageId": "594", "endLine": 12, "endColumn": 8}, {"ruleId": "592", "severity": 2, "message": "595", "line": 17, "column": 3, "nodeType": null, "messageId": "594", "endLine": 17, "endColumn": 8}, {"ruleId": "592", "severity": 2, "message": "596", "line": 18, "column": 3, "nodeType": null, "messageId": "594", "endLine": 18, "endColumn": 11}, {"ruleId": "592", "severity": 2, "message": "597", "line": 21, "column": 3, "nodeType": null, "messageId": "594", "endLine": 21, "endColumn": 7}, {"ruleId": "592", "severity": 2, "message": "598", "line": 26, "column": 7, "nodeType": null, "messageId": "594", "endLine": 26, "endColumn": 21}, {"ruleId": "599", "severity": 1, "message": "600", "line": 49, "column": 6, "nodeType": "601", "endLine": 49, "endColumn": 17, "suggestions": "602"}, {"ruleId": "592", "severity": 2, "message": "603", "line": 63, "column": 14, "nodeType": null, "messageId": "594", "endLine": 63, "endColumn": 17}, {"ruleId": "592", "severity": 2, "message": "604", "line": 70, "column": 9, "nodeType": null, "messageId": "594", "endLine": 70, "endColumn": 19}, {"ruleId": "592", "severity": 2, "message": "605", "line": 79, "column": 9, "nodeType": null, "messageId": "594", "endLine": 79, "endColumn": 24}, {"ruleId": "592", "severity": 2, "message": "606", "line": 92, "column": 9, "nodeType": null, "messageId": "594", "endLine": 92, "endColumn": 27}, {"ruleId": "592", "severity": 2, "message": "607", "line": 18, "column": 3, "nodeType": null, "messageId": "594", "endLine": 18, "endColumn": 17}, {"ruleId": "599", "severity": 1, "message": "608", "line": 62, "column": 6, "nodeType": "601", "endLine": 62, "endColumn": 20, "suggestions": "609"}, {"ruleId": "592", "severity": 2, "message": "603", "line": 79, "column": 14, "nodeType": null, "messageId": "594", "endLine": 79, "endColumn": 17}, {"ruleId": "592", "severity": 2, "message": "603", "line": 112, "column": 14, "nodeType": null, "messageId": "594", "endLine": 112, "endColumn": 17}, {"ruleId": "592", "severity": 2, "message": "603", "line": 138, "column": 14, "nodeType": null, "messageId": "594", "endLine": 138, "endColumn": 17}, {"ruleId": "610", "severity": 1, "message": "611", "line": 308, "column": 27, "nodeType": "612", "endLine": 312, "endColumn": 29}, {"ruleId": "592", "severity": 2, "message": "613", "line": 74, "column": 9, "nodeType": null, "messageId": "594", "endLine": 74, "endColumn": 15}, {"ruleId": "592", "severity": 2, "message": "614", "line": 88, "column": 14, "nodeType": null, "messageId": "594", "endLine": 88, "endColumn": 19}, {"ruleId": "592", "severity": 2, "message": "614", "line": 104, "column": 14, "nodeType": null, "messageId": "594", "endLine": 104, "endColumn": 19}, {"ruleId": "610", "severity": 1, "message": "611", "line": 179, "column": 15, "nodeType": "612", "endLine": 183, "endColumn": 17}, {"ruleId": "610", "severity": 1, "message": "611", "line": 273, "column": 19, "nodeType": "612", "endLine": 278, "endColumn": 21}, {"ruleId": "615", "severity": 2, "message": "616", "line": 19, "column": 18, "nodeType": "617", "messageId": "618", "endLine": 19, "endColumn": 21, "suggestions": "619"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 55, "column": 22, "nodeType": "617", "messageId": "618", "endLine": 55, "endColumn": 25, "suggestions": "620"}, {"ruleId": "592", "severity": 2, "message": "621", "line": 8, "column": 27, "nodeType": null, "messageId": "594", "endLine": 8, "endColumn": 34}, {"ruleId": "615", "severity": 2, "message": "616", "line": 96, "column": 23, "nodeType": "617", "messageId": "618", "endLine": 96, "endColumn": 26, "suggestions": "622"}, {"ruleId": "592", "severity": 2, "message": "623", "line": 1, "column": 10, "nodeType": null, "messageId": "594", "endLine": 1, "endColumn": 21}, {"ruleId": "615", "severity": 2, "message": "616", "line": 179, "column": 20, "nodeType": "617", "messageId": "618", "endLine": 179, "endColumn": 23, "suggestions": "624"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 217, "column": 70, "nodeType": "617", "messageId": "618", "endLine": 217, "endColumn": 73, "suggestions": "625"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 22, "column": 18, "nodeType": "617", "messageId": "618", "endLine": 22, "endColumn": 21, "suggestions": "626"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 66, "column": 22, "nodeType": "617", "messageId": "618", "endLine": 66, "endColumn": 25, "suggestions": "627"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 168, "column": 70, "nodeType": "617", "messageId": "618", "endLine": 168, "endColumn": 73, "suggestions": "628"}, {"ruleId": "592", "severity": 2, "message": "614", "line": 56, "column": 14, "nodeType": null, "messageId": "594", "endLine": 56, "endColumn": 19}, {"ruleId": "592", "severity": 2, "message": "614", "line": 109, "column": 12, "nodeType": null, "messageId": "594", "endLine": 109, "endColumn": 17}, {"ruleId": "629", "severity": 2, "message": "630", "line": 226, "column": 15, "nodeType": "612", "endLine": 229, "endColumn": 16}, {"ruleId": "629", "severity": 2, "message": "631", "line": 238, "column": 17, "nodeType": "612", "endLine": 241, "endColumn": 18}, {"ruleId": "592", "severity": 2, "message": "598", "line": 23, "column": 7, "nodeType": null, "messageId": "594", "endLine": 23, "endColumn": 21}, {"ruleId": "592", "severity": 2, "message": "603", "line": 100, "column": 14, "nodeType": null, "messageId": "594", "endLine": 100, "endColumn": 17}, {"ruleId": "592", "severity": 2, "message": "614", "line": 66, "column": 12, "nodeType": null, "messageId": "594", "endLine": 66, "endColumn": 17}, {"ruleId": "599", "severity": 1, "message": "632", "line": 46, "column": 6, "nodeType": "601", "endLine": 46, "endColumn": 49, "suggestions": "633"}, {"ruleId": "592", "severity": 2, "message": "603", "line": 61, "column": 14, "nodeType": null, "messageId": "594", "endLine": 61, "endColumn": 17}, {"ruleId": "592", "severity": 2, "message": "634", "line": 11, "column": 3, "nodeType": null, "messageId": "594", "endLine": 11, "endColumn": 7}, {"ruleId": "592", "severity": 2, "message": "635", "line": 18, "column": 3, "nodeType": null, "messageId": "594", "endLine": 18, "endColumn": 7}, {"ruleId": "610", "severity": 1, "message": "611", "line": 97, "column": 19, "nodeType": "612", "endLine": 101, "endColumn": 21}, {"ruleId": "592", "severity": 2, "message": "614", "line": 104, "column": 14, "nodeType": null, "messageId": "594", "endLine": 104, "endColumn": 19}, {"ruleId": "592", "severity": 2, "message": "636", "line": 12, "column": 3, "nodeType": null, "messageId": "594", "endLine": 12, "endColumn": 7}, {"ruleId": "592", "severity": 2, "message": "637", "line": 15, "column": 3, "nodeType": null, "messageId": "594", "endLine": 15, "endColumn": 6}, {"ruleId": "592", "severity": 2, "message": "603", "line": 94, "column": 14, "nodeType": null, "messageId": "594", "endLine": 94, "endColumn": 17}, {"ruleId": "592", "severity": 2, "message": "603", "line": 111, "column": 14, "nodeType": null, "messageId": "594", "endLine": 111, "endColumn": 17}, {"ruleId": "592", "severity": 2, "message": "603", "line": 128, "column": 14, "nodeType": null, "messageId": "594", "endLine": 128, "endColumn": 17}, {"ruleId": "610", "severity": 1, "message": "611", "line": 197, "column": 23, "nodeType": "612", "endLine": 201, "endColumn": 25}, {"ruleId": "610", "severity": 1, "message": "611", "line": 289, "column": 21, "nodeType": "612", "endLine": 293, "endColumn": 23}, {"ruleId": "592", "severity": 2, "message": "614", "line": 27, "column": 14, "nodeType": null, "messageId": "594", "endLine": 27, "endColumn": 19}, {"ruleId": "592", "severity": 2, "message": "614", "line": 46, "column": 14, "nodeType": null, "messageId": "594", "endLine": 46, "endColumn": 19}, {"ruleId": "615", "severity": 2, "message": "616", "line": 63, "column": 42, "nodeType": "617", "messageId": "618", "endLine": 63, "endColumn": 45, "suggestions": "638"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 64, "column": 42, "nodeType": "617", "messageId": "618", "endLine": 64, "endColumn": 45, "suggestions": "639"}, {"ruleId": "592", "severity": 2, "message": "614", "line": 69, "column": 12, "nodeType": null, "messageId": "594", "endLine": 69, "endColumn": 17}, {"ruleId": "592", "severity": 2, "message": "614", "line": 55, "column": 12, "nodeType": null, "messageId": "594", "endLine": 55, "endColumn": 17}, {"ruleId": "629", "severity": 2, "message": "630", "line": 126, "column": 15, "nodeType": "612", "endLine": 129, "endColumn": 16}, {"ruleId": "592", "severity": 2, "message": "614", "line": 44, "column": 14, "nodeType": null, "messageId": "594", "endLine": 44, "endColumn": 19}, {"ruleId": "592", "severity": 2, "message": "614", "line": 84, "column": 14, "nodeType": null, "messageId": "594", "endLine": 84, "endColumn": 19}, {"ruleId": "592", "severity": 2, "message": "614", "line": 111, "column": 14, "nodeType": null, "messageId": "594", "endLine": 111, "endColumn": 19}, {"ruleId": "610", "severity": 1, "message": "611", "line": 61, "column": 13, "nodeType": "612", "endLine": 65, "endColumn": 15}, {"ruleId": "610", "severity": 1, "message": "611", "line": 93, "column": 21, "nodeType": "612", "endLine": 97, "endColumn": 23}, {"ruleId": "592", "severity": 2, "message": "640", "line": 5, "column": 27, "nodeType": null, "messageId": "594", "endLine": 5, "endColumn": 34}, {"ruleId": "592", "severity": 2, "message": "641", "line": 5, "column": 36, "nodeType": null, "messageId": "594", "endLine": 5, "endColumn": 46}, {"ruleId": "599", "severity": 1, "message": "642", "line": 55, "column": 6, "nodeType": "601", "endLine": 55, "endColumn": 14, "suggestions": "643"}, {"ruleId": "610", "severity": 1, "message": "611", "line": 156, "column": 13, "nodeType": "612", "endLine": 160, "endColumn": 15}, {"ruleId": "599", "severity": 1, "message": "644", "line": 38, "column": 6, "nodeType": "601", "endLine": 38, "endColumn": 42, "suggestions": "645"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 4, "column": 34, "nodeType": "617", "messageId": "618", "endLine": 4, "endColumn": 37, "suggestions": "646"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 65, "column": 60, "nodeType": "617", "messageId": "618", "endLine": 65, "endColumn": 63, "suggestions": "647"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 137, "column": 18, "nodeType": "617", "messageId": "618", "endLine": 137, "endColumn": 21, "suggestions": "648"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 149, "column": 31, "nodeType": "617", "messageId": "618", "endLine": 149, "endColumn": 34, "suggestions": "649"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 166, "column": 26, "nodeType": "617", "messageId": "618", "endLine": 166, "endColumn": 29, "suggestions": "650"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 167, "column": 26, "nodeType": "617", "messageId": "618", "endLine": 167, "endColumn": 29, "suggestions": "651"}, {"ruleId": "592", "severity": 2, "message": "652", "line": 1, "column": 8, "nodeType": null, "messageId": "594", "endLine": 1, "endColumn": 16}, {"ruleId": "592", "severity": 2, "message": "653", "line": 27, "column": 13, "nodeType": null, "messageId": "594", "endLine": 27, "endColumn": 26}, {"ruleId": "592", "severity": 2, "message": "654", "line": 5, "column": 8, "nodeType": null, "messageId": "594", "endLine": 5, "endColumn": 12}, {"ruleId": "592", "severity": 2, "message": "655", "line": 89, "column": 11, "nodeType": null, "messageId": "594", "endLine": 89, "endColumn": 14}, {"ruleId": "615", "severity": 2, "message": "616", "line": 159, "column": 25, "nodeType": "617", "messageId": "618", "endLine": 159, "endColumn": 28, "suggestions": "656"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 38, "column": 18, "nodeType": "617", "messageId": "618", "endLine": 38, "endColumn": 21, "suggestions": "657"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 51, "column": 22, "nodeType": "617", "messageId": "618", "endLine": 51, "endColumn": 25, "suggestions": "658"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 88, "column": 52, "nodeType": "617", "messageId": "618", "endLine": 88, "endColumn": 55, "suggestions": "659"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 89, "column": 52, "nodeType": "617", "messageId": "618", "endLine": 89, "endColumn": 55, "suggestions": "660"}, {"ruleId": "592", "severity": 2, "message": "661", "line": 19, "column": 17, "nodeType": null, "messageId": "594", "endLine": 19, "endColumn": 24}, {"ruleId": "615", "severity": 2, "message": "616", "line": 20, "column": 38, "nodeType": "617", "messageId": "618", "endLine": 20, "endColumn": 41, "suggestions": "662"}, {"ruleId": "599", "severity": 1, "message": "663", "line": 36, "column": 6, "nodeType": "601", "endLine": 36, "endColumn": 23, "suggestions": "664"}, {"ruleId": "592", "severity": 2, "message": "603", "line": 62, "column": 14, "nodeType": null, "messageId": "594", "endLine": 62, "endColumn": 17}, {"ruleId": "592", "severity": 2, "message": "603", "line": 86, "column": 14, "nodeType": null, "messageId": "594", "endLine": 86, "endColumn": 17}, {"ruleId": "615", "severity": 2, "message": "616", "line": 16, "column": 36, "nodeType": "617", "messageId": "618", "endLine": 16, "endColumn": 39, "suggestions": "665"}, {"ruleId": "599", "severity": 1, "message": "666", "line": 32, "column": 6, "nodeType": "601", "endLine": 32, "endColumn": 22, "suggestions": "667"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 18, "column": 36, "nodeType": "617", "messageId": "618", "endLine": 18, "endColumn": 39, "suggestions": "668"}, {"ruleId": "599", "severity": 1, "message": "666", "line": 32, "column": 6, "nodeType": "601", "endLine": 32, "endColumn": 22, "suggestions": "669"}, {"ruleId": "592", "severity": 2, "message": "661", "line": 12, "column": 17, "nodeType": null, "messageId": "594", "endLine": 12, "endColumn": 24}, {"ruleId": "615", "severity": 2, "message": "616", "line": 13, "column": 36, "nodeType": "617", "messageId": "618", "endLine": 13, "endColumn": 39, "suggestions": "670"}, {"ruleId": "599", "severity": 1, "message": "666", "line": 28, "column": 6, "nodeType": "601", "endLine": 28, "endColumn": 22, "suggestions": "671"}, {"ruleId": "592", "severity": 2, "message": "614", "line": 81, "column": 14, "nodeType": null, "messageId": "594", "endLine": 81, "endColumn": 19}, {"ruleId": "592", "severity": 2, "message": "603", "line": 55, "column": 14, "nodeType": null, "messageId": "594", "endLine": 55, "endColumn": 17}, {"ruleId": "610", "severity": 1, "message": "611", "line": 483, "column": 19, "nodeType": "612", "endLine": 487, "endColumn": 21}, {"ruleId": "615", "severity": 2, "message": "616", "line": 50, "column": 33, "nodeType": "617", "messageId": "618", "endLine": 50, "endColumn": 36, "suggestions": "672"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 63, "column": 33, "nodeType": "617", "messageId": "618", "endLine": 63, "endColumn": 36, "suggestions": "673"}, {"ruleId": "615", "severity": 2, "message": "616", "line": 185, "column": 22, "nodeType": "617", "messageId": "618", "endLine": 185, "endColumn": 25, "suggestions": "674"}, {"ruleId": "592", "severity": 2, "message": "675", "line": 25, "column": 16, "nodeType": null, "messageId": "594", "endLine": 25, "endColumn": 23}, {"ruleId": "599", "severity": 1, "message": "676", "line": 31, "column": 6, "nodeType": "601", "endLine": 31, "endColumn": 21, "suggestions": "677"}, {"ruleId": "592", "severity": 2, "message": "603", "line": 46, "column": 14, "nodeType": null, "messageId": "594", "endLine": 46, "endColumn": 17}, {"ruleId": "610", "severity": 1, "message": "611", "line": 61, "column": 19, "nodeType": "612", "endLine": 65, "endColumn": 21}, {"ruleId": "610", "severity": 1, "message": "611", "line": 190, "column": 27, "nodeType": "612", "endLine": 194, "endColumn": 29}, {"ruleId": "610", "severity": 1, "message": "611", "line": 180, "column": 7, "nodeType": "612", "endLine": 189, "endColumn": 9}, {"ruleId": "592", "severity": 2, "message": "678", "line": 37, "column": 10, "nodeType": null, "messageId": "594", "endLine": 37, "endColumn": 18}, {"ruleId": "679", "severity": 1, "message": "680", "line": 78, "column": 9, "nodeType": "612", "endLine": 82, "endColumn": 11}, {"ruleId": "679", "severity": 1, "message": "680", "line": 92, "column": 7, "nodeType": "612", "endLine": 96, "endColumn": 9}, {"ruleId": "592", "severity": 2, "message": "614", "line": 54, "column": 12, "nodeType": null, "messageId": "594", "endLine": 54, "endColumn": 17}, {"ruleId": "629", "severity": 2, "message": "630", "line": 163, "column": 15, "nodeType": "612", "endLine": 166, "endColumn": 16}, {"ruleId": "629", "severity": 2, "message": "630", "line": 89, "column": 15, "nodeType": "612", "endLine": 92, "endColumn": 16}, {"ruleId": "592", "severity": 2, "message": "681", "line": 5, "column": 38, "nodeType": null, "messageId": "594", "endLine": 5, "endColumn": 44}, {"ruleId": "629", "severity": 2, "message": "630", "line": 99, "column": 15, "nodeType": "612", "endLine": 102, "endColumn": 16}, {"ruleId": "629", "severity": 2, "message": "630", "line": 66, "column": 15, "nodeType": "612", "endLine": 69, "endColumn": 16}, {"ruleId": "629", "severity": 2, "message": "630", "line": 66, "column": 15, "nodeType": "612", "endLine": 69, "endColumn": 16}, {"ruleId": "682", "severity": 2, "message": "683", "line": 127, "column": 25, "nodeType": "684", "messageId": "685", "suggestions": "686"}, {"ruleId": "682", "severity": 2, "message": "683", "line": 127, "column": 28, "nodeType": "684", "messageId": "685", "suggestions": "687"}, {"ruleId": "682", "severity": 2, "message": "683", "line": 127, "column": 30, "nodeType": "684", "messageId": "685", "suggestions": "688"}, {"ruleId": "682", "severity": 2, "message": "683", "line": 127, "column": 34, "nodeType": "684", "messageId": "685", "suggestions": "689"}, {"ruleId": "682", "severity": 2, "message": "683", "line": 219, "column": 23, "nodeType": "684", "messageId": "685", "suggestions": "690"}, {"ruleId": "682", "severity": 2, "message": "683", "line": 219, "column": 26, "nodeType": "684", "messageId": "685", "suggestions": "691"}, {"ruleId": "599", "severity": 1, "message": "692", "line": 176, "column": 6, "nodeType": "601", "endLine": 176, "endColumn": 15, "suggestions": "693"}, {"ruleId": "592", "severity": 2, "message": "694", "line": 5, "column": 23, "nodeType": null, "messageId": "594", "endLine": 5, "endColumn": 26}, "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'categoryLabels' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["695"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'MoreHorizontal' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["696"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'params' is assigned a value but never used.", "'error' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["697", "698"], ["699", "700"], "'request' is defined but never used.", ["701", "702"], "'NextRequest' is defined but never used.", ["703", "704"], ["705", "706"], ["707", "708"], ["709", "710"], ["711", "712"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/categories/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["713"], "'User' is defined but never used.", "'Edit' is defined but never used.", "'Mail' is defined but never used.", "'Eye' is defined but never used.", ["714", "715"], ["716", "717"], "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["718"], "React Hook useEffect has a missing dependency: 'initializeToolState'. Either include it or remove the dependency array.", ["719"], ["720", "721"], ["722", "723"], ["724", "725"], ["726", "727"], ["728", "729"], ["730", "731"], "'mongoose' is defined but never used.", "'paymentMethod' is assigned a value but never used.", "'Tool' is defined but never used.", "'now' is assigned a value but never used.", ["732", "733"], ["734", "735"], ["736", "737"], ["738", "739"], ["740", "741"], "'session' is assigned a value but never used.", ["742", "743"], "React Hook useEffect has missing dependencies: 'fetchOrderInfo' and 'router'. Either include them or remove the dependency array.", ["744"], ["745", "746"], "React Hook useEffect has missing dependencies: 'fetchToolInfo' and 'router'. Either include them or remove the dependency array.", ["747"], ["748", "749"], ["750"], ["751", "752"], ["753"], ["754", "755"], ["756", "757"], ["758", "759"], "'setTool' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchRelatedTools'. Either include it or remove the dependency array.", ["760"], "'hasError' is assigned a value but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'MapPin' is defined but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["761", "762", "763", "764"], ["765", "766", "767", "768"], ["769", "770", "771", "772"], ["773", "774", "775", "776"], ["777", "778", "779", "780"], ["781", "782", "783", "784"], "React Hook useEffect has missing dependencies: 'refreshToolState' and 'toolStates'. Either include them or remove the dependency array.", ["785"], "'req' is defined but never used.", {"desc": "786", "fix": "787"}, {"desc": "788", "fix": "789"}, {"messageId": "790", "fix": "791", "desc": "792"}, {"messageId": "793", "fix": "794", "desc": "795"}, {"messageId": "790", "fix": "796", "desc": "792"}, {"messageId": "793", "fix": "797", "desc": "795"}, {"messageId": "790", "fix": "798", "desc": "792"}, {"messageId": "793", "fix": "799", "desc": "795"}, {"messageId": "790", "fix": "800", "desc": "792"}, {"messageId": "793", "fix": "801", "desc": "795"}, {"messageId": "790", "fix": "802", "desc": "792"}, {"messageId": "793", "fix": "803", "desc": "795"}, {"messageId": "790", "fix": "804", "desc": "792"}, {"messageId": "793", "fix": "805", "desc": "795"}, {"messageId": "790", "fix": "806", "desc": "792"}, {"messageId": "793", "fix": "807", "desc": "795"}, {"messageId": "790", "fix": "808", "desc": "792"}, {"messageId": "793", "fix": "809", "desc": "795"}, {"desc": "810", "fix": "811"}, {"messageId": "790", "fix": "812", "desc": "792"}, {"messageId": "793", "fix": "813", "desc": "795"}, {"messageId": "790", "fix": "814", "desc": "792"}, {"messageId": "793", "fix": "815", "desc": "795"}, {"desc": "816", "fix": "817"}, {"desc": "818", "fix": "819"}, {"messageId": "790", "fix": "820", "desc": "792"}, {"messageId": "793", "fix": "821", "desc": "795"}, {"messageId": "790", "fix": "822", "desc": "792"}, {"messageId": "793", "fix": "823", "desc": "795"}, {"messageId": "790", "fix": "824", "desc": "792"}, {"messageId": "793", "fix": "825", "desc": "795"}, {"messageId": "790", "fix": "826", "desc": "792"}, {"messageId": "793", "fix": "827", "desc": "795"}, {"messageId": "790", "fix": "828", "desc": "792"}, {"messageId": "793", "fix": "829", "desc": "795"}, {"messageId": "790", "fix": "830", "desc": "792"}, {"messageId": "793", "fix": "831", "desc": "795"}, {"messageId": "790", "fix": "832", "desc": "792"}, {"messageId": "793", "fix": "833", "desc": "795"}, {"messageId": "790", "fix": "834", "desc": "792"}, {"messageId": "793", "fix": "835", "desc": "795"}, {"messageId": "790", "fix": "836", "desc": "792"}, {"messageId": "793", "fix": "837", "desc": "795"}, {"messageId": "790", "fix": "838", "desc": "792"}, {"messageId": "793", "fix": "839", "desc": "795"}, {"messageId": "790", "fix": "840", "desc": "792"}, {"messageId": "793", "fix": "841", "desc": "795"}, {"messageId": "790", "fix": "842", "desc": "792"}, {"messageId": "793", "fix": "843", "desc": "795"}, {"desc": "844", "fix": "845"}, {"messageId": "790", "fix": "846", "desc": "792"}, {"messageId": "793", "fix": "847", "desc": "795"}, {"desc": "848", "fix": "849"}, {"messageId": "790", "fix": "850", "desc": "792"}, {"messageId": "793", "fix": "851", "desc": "795"}, {"desc": "848", "fix": "852"}, {"messageId": "790", "fix": "853", "desc": "792"}, {"messageId": "793", "fix": "854", "desc": "795"}, {"desc": "848", "fix": "855"}, {"messageId": "790", "fix": "856", "desc": "792"}, {"messageId": "793", "fix": "857", "desc": "795"}, {"messageId": "790", "fix": "858", "desc": "792"}, {"messageId": "793", "fix": "859", "desc": "795"}, {"messageId": "790", "fix": "860", "desc": "792"}, {"messageId": "793", "fix": "861", "desc": "795"}, {"desc": "862", "fix": "863"}, {"messageId": "864", "data": "865", "fix": "866", "desc": "867"}, {"messageId": "864", "data": "868", "fix": "869", "desc": "870"}, {"messageId": "864", "data": "871", "fix": "872", "desc": "873"}, {"messageId": "864", "data": "874", "fix": "875", "desc": "876"}, {"messageId": "864", "data": "877", "fix": "878", "desc": "867"}, {"messageId": "864", "data": "879", "fix": "880", "desc": "870"}, {"messageId": "864", "data": "881", "fix": "882", "desc": "873"}, {"messageId": "864", "data": "883", "fix": "884", "desc": "876"}, {"messageId": "864", "data": "885", "fix": "886", "desc": "867"}, {"messageId": "864", "data": "887", "fix": "888", "desc": "870"}, {"messageId": "864", "data": "889", "fix": "890", "desc": "873"}, {"messageId": "864", "data": "891", "fix": "892", "desc": "876"}, {"messageId": "864", "data": "893", "fix": "894", "desc": "867"}, {"messageId": "864", "data": "895", "fix": "896", "desc": "870"}, {"messageId": "864", "data": "897", "fix": "898", "desc": "873"}, {"messageId": "864", "data": "899", "fix": "900", "desc": "876"}, {"messageId": "864", "data": "901", "fix": "902", "desc": "867"}, {"messageId": "864", "data": "903", "fix": "904", "desc": "870"}, {"messageId": "864", "data": "905", "fix": "906", "desc": "873"}, {"messageId": "864", "data": "907", "fix": "908", "desc": "876"}, {"messageId": "864", "data": "909", "fix": "910", "desc": "867"}, {"messageId": "864", "data": "911", "fix": "912", "desc": "870"}, {"messageId": "864", "data": "913", "fix": "914", "desc": "873"}, {"messageId": "864", "data": "915", "fix": "916", "desc": "876"}, {"desc": "917", "fix": "918"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "919", "text": "920"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "921", "text": "922"}, "suggestUnknown", {"range": "923", "text": "924"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "925", "text": "926"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "927", "text": "924"}, {"range": "928", "text": "926"}, {"range": "929", "text": "924"}, {"range": "930", "text": "926"}, {"range": "931", "text": "924"}, {"range": "932", "text": "926"}, {"range": "933", "text": "924"}, {"range": "934", "text": "926"}, {"range": "935", "text": "924"}, {"range": "936", "text": "926"}, {"range": "937", "text": "924"}, {"range": "938", "text": "926"}, {"range": "939", "text": "924"}, {"range": "940", "text": "926"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "941", "text": "942"}, {"range": "943", "text": "924"}, {"range": "944", "text": "926"}, {"range": "945", "text": "924"}, {"range": "946", "text": "926"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "947", "text": "948"}, "Update the dependencies array to be: [toolId, initialLikes, initialLiked, initializeToolState]", {"range": "949", "text": "950"}, {"range": "951", "text": "924"}, {"range": "952", "text": "926"}, {"range": "953", "text": "924"}, {"range": "954", "text": "926"}, {"range": "955", "text": "924"}, {"range": "956", "text": "926"}, {"range": "957", "text": "924"}, {"range": "958", "text": "926"}, {"range": "959", "text": "924"}, {"range": "960", "text": "926"}, {"range": "961", "text": "924"}, {"range": "962", "text": "926"}, {"range": "963", "text": "924"}, {"range": "964", "text": "926"}, {"range": "965", "text": "924"}, {"range": "966", "text": "926"}, {"range": "967", "text": "924"}, {"range": "968", "text": "926"}, {"range": "969", "text": "924"}, {"range": "970", "text": "926"}, {"range": "971", "text": "924"}, {"range": "972", "text": "926"}, {"range": "973", "text": "924"}, {"range": "974", "text": "926"}, "Update the dependencies array to be: [status, orderId, router, fetchOrderInfo]", {"range": "975", "text": "976"}, {"range": "977", "text": "924"}, {"range": "978", "text": "926"}, "Update the dependencies array to be: [fetchToolInfo, router, status, toolId]", {"range": "979", "text": "980"}, {"range": "981", "text": "924"}, {"range": "982", "text": "926"}, {"range": "983", "text": "980"}, {"range": "984", "text": "924"}, {"range": "985", "text": "926"}, {"range": "986", "text": "980"}, {"range": "987", "text": "924"}, {"range": "988", "text": "926"}, {"range": "989", "text": "924"}, {"range": "990", "text": "926"}, {"range": "991", "text": "924"}, {"range": "992", "text": "926"}, "Update the dependencies array to be: [fetchRelatedTools, tool.category]", {"range": "993", "text": "994"}, "replaceWithAlt", {"alt": "995"}, {"range": "996", "text": "997"}, "Replace with `&quot;`.", {"alt": "998"}, {"range": "999", "text": "1000"}, "Replace with `&ldquo;`.", {"alt": "1001"}, {"range": "1002", "text": "1003"}, "Replace with `&#34;`.", {"alt": "1004"}, {"range": "1005", "text": "1006"}, "Replace with `&rdquo;`.", {"alt": "995"}, {"range": "1007", "text": "1008"}, {"alt": "998"}, {"range": "1009", "text": "1010"}, {"alt": "1001"}, {"range": "1011", "text": "1012"}, {"alt": "1004"}, {"range": "1013", "text": "1014"}, {"alt": "995"}, {"range": "1015", "text": "1016"}, {"alt": "998"}, {"range": "1017", "text": "1018"}, {"alt": "1001"}, {"range": "1019", "text": "1020"}, {"alt": "1004"}, {"range": "1021", "text": "1022"}, {"alt": "995"}, {"range": "1023", "text": "1024"}, {"alt": "998"}, {"range": "1025", "text": "1026"}, {"alt": "1001"}, {"range": "1027", "text": "1028"}, {"alt": "1004"}, {"range": "1029", "text": "1030"}, {"alt": "995"}, {"range": "1031", "text": "1032"}, {"alt": "998"}, {"range": "1033", "text": "1034"}, {"alt": "1001"}, {"range": "1035", "text": "1036"}, {"alt": "1004"}, {"range": "1037", "text": "1038"}, {"alt": "995"}, {"range": "1039", "text": "1040"}, {"alt": "998"}, {"range": "1041", "text": "1042"}, {"alt": "1001"}, {"range": "1043", "text": "1044"}, {"alt": "1004"}, {"range": "1045", "text": "1046"}, "Update the dependencies array to be: [refreshToolState, session, toolStates]", {"range": "1047", "text": "1048"}, [1183, 1194], "[fetchStats, timeRange]", [1704, 1718], "[fetchTools, statusFilter]", [672, 675], "unknown", [672, 675], "never", [1551, 1554], [1551, 1554], [2591, 2594], [2591, 2594], [4541, 4544], [4541, 4544], [5532, 5535], [5532, 5535], [802, 805], [802, 805], [1909, 1912], [1909, 1912], [4431, 4434], [4431, 4434], [1329, 1372], "[filterTools, likedTools, searchQuery, selectedCategory]", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [1439, 1447], "[fetchComments, toolId]", [945, 981], "[toolId, initialLikes, initialLiked, initializeToolState]", [137, 140], [137, 140], [2074, 2077], [2074, 2077], [4211, 4214], [4211, 4214], [4539, 4542], [4539, 4542], [5047, 5050], [5047, 5050], [5106, 5109], [5106, 5109], [4266, 4269], [4266, 4269], [1171, 1174], [1171, 1174], [1442, 1445], [1442, 1445], [2429, 2432], [2429, 2432], [2517, 2520], [2517, 2520], [809, 812], [809, 812], [1213, 1230], "[status, orderId, router, fetchOrderInfo]", [617, 620], [617, 620], [910, 926], "[fetchToolInfo, router, status, toolId]", [604, 607], [604, 607], [895, 911], [511, 514], [511, 514], [859, 875], [1561, 1564], [1561, 1564], [2024, 2027], [2024, 2027], [4765, 4768], [4765, 4768], [908, 923], "[fetchRelatedTools, tool.category]", "&quot;", [4472, 4520], "AI工具导航（以下简称&quot;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&ldquo;", [4472, 4520], "AI工具导航（以下简称&ldquo;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&#34;", [4472, 4520], "AI工具导航（以下简称&#34;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&rdquo;", [4472, 4520], "AI工具导航（以下简称&rdquo;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&quot;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&ldquo;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&#34;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&rdquo;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&quot;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&ldquo;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&#34;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&rdquo;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&quot;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&ldquo;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&#34;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&rdquo;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [6921, 6947], "我们的服务按&quot;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&ldquo;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&#34;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&rdquo;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&quot;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&ldquo;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&#34;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&rdquo;提供，不提供任何明示或暗示的保证", [4405, 4414], "[refreshToolState, session, toolStates]"]