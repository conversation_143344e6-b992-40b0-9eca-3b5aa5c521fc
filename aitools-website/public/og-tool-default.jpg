<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1200" height="630" fill="url(#gradient)"/>
<defs>
<linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
<stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
</linearGradient>
</defs>
<rect x="100" y="200" width="120" height="120" rx="20" fill="white" fill-opacity="0.2"/>
<circle cx="140" cy="240" r="15" fill="white" fill-opacity="0.8"/>
<path d="M120 280L160 240L200 280H120Z" fill="white" fill-opacity="0.8"/>
<text x="300" y="280" fill="white" font-family="Arial" font-size="48" font-weight="bold">AI工具导航</text>
<text x="300" y="330" fill="white" font-family="Arial" font-size="24" opacity="0.9">发现最新最好的AI工具</text>
<text x="300" y="380" fill="white" font-family="Arial" font-size="18" opacity="0.7">提升您的工作效率和创造力</text>
</svg>
