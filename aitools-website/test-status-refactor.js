// 测试工具状态重构的脚本
// 这个脚本验证新的状态系统是否正确工作

const mongoose = require('mongoose');

// 连接数据库
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aitools');
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
}

// 工具模型定义（简化版）
const ToolSchema = new mongoose.Schema({
  name: String,
  status: {
    type: String,
    enum: ['draft', 'pending', 'approved', 'rejected']
  },
  launchDate: Date,
  selectedLaunchDate: Date,
  submittedAt: { type: Date, default: Date.now },
  views: { type: Number, default: 0 },
  likes: { type: Number, default: 0 }
});

const Tool = mongoose.model('Tool', ToolSchema);

// 测试函数
async function testStatusRefactor() {
  console.log('\n🧪 开始测试工具状态重构...\n');

  try {
    // 1. 测试查询已发布的工具
    console.log('1. 测试查询已发布的工具...');
    const publishedTools = await Tool.find({
      status: 'approved',
      launchDate: { $lte: new Date() }
    }).limit(5);
    
    console.log(`   找到 ${publishedTools.length} 个已发布的工具`);
    publishedTools.forEach(tool => {
      const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date();
      console.log(`   - ${tool.name}: ${isPublished ? '✅ 已发布' : '❌ 未发布'}`);
    });

    // 2. 测试查询待发布的工具
    console.log('\n2. 测试查询待发布的工具...');
    const pendingPublish = await Tool.find({
      status: 'approved',
      $or: [
        { launchDate: { $gt: new Date() } },
        { launchDate: { $exists: false } }
      ]
    }).limit(5);
    
    console.log(`   找到 ${pendingPublish.length} 个待发布的工具`);
    pendingPublish.forEach(tool => {
      const willPublish = tool.selectedLaunchDate ? new Date(tool.selectedLaunchDate) : null;
      console.log(`   - ${tool.name}: 计划发布时间 ${willPublish ? willPublish.toLocaleDateString() : '未设置'}`);
    });

    // 3. 测试状态统计
    console.log('\n3. 测试状态统计...');
    const stats = await Tool.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);
    
    console.log('   状态分布:');
    stats.forEach(stat => {
      console.log(`   - ${stat._id}: ${stat.count} 个`);
    });

    // 4. 测试发布逻辑
    console.log('\n4. 测试发布逻辑...');
    const now = new Date();
    const publishableTools = await Tool.find({
      status: 'approved',
      selectedLaunchDate: { $lte: now },
      $or: [
        { launchDate: { $exists: false } },
        { launchDate: { $gt: now } }
      ]
    }).limit(3);

    console.log(`   找到 ${publishableTools.length} 个可以发布的工具`);
    
    for (const tool of publishableTools) {
      console.log(`   - 模拟发布: ${tool.name}`);
      // 这里只是模拟，不实际更新数据库
      // await Tool.findByIdAndUpdate(tool._id, { launchDate: tool.selectedLaunchDate || now });
    }

    console.log('\n✅ 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 主函数
async function main() {
  await connectDB();
  await testStatusRefactor();
  await mongoose.disconnect();
  console.log('\n👋 测试结束，数据库连接已关闭');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testStatusRefactor };
