import dbConnect from './mongodb';
import Category from '../models/Category';
import Tool from '../models/Tool';
import User from '../models/User';
import { CATEGORY_CONFIGS } from '../constants/categories';

export const seedCategories = async () => {
  await dbConnect();

  // 使用统一的分类配置，转换为数据库格式
  const categories = CATEGORY_CONFIGS.map(config => ({
    name: config.name,
    slug: config.slug,
    description: config.description,
    icon: config.icon,
    color: config.color
  }));

  try {
    // Clear existing categories
    await Category.deleteMany({});
    
    // Insert new categories
    const insertedCategories = await Category.insertMany(categories);
    console.log(`Seeded ${insertedCategories.length} categories`);
    
    return insertedCategories;
  } catch (error) {
    console.error('Error seeding categories:', error);
    throw error;
  }
};

export const seedUsers = async () => {
  await dbConnect();

  const users = [
    {
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin'
    },
    {
      email: '<EMAIL>',
      name: 'Demo User',
      role: 'user'
    }
  ];

  try {
    // Clear existing users
    await User.deleteMany({});
    
    // Insert new users
    const insertedUsers = await User.insertMany(users);
    console.log(`Seeded ${insertedUsers.length} users`);
    
    return insertedUsers;
  } catch (error) {
    console.error('Error seeding users:', error);
    throw error;
  }
};

export const seedTools = async () => {
  await dbConnect();

  // Get admin user for submittedBy field
  const adminUser = await User.findOne({ role: 'admin' });
  if (!adminUser) {
    throw new Error('Admin user not found. Please seed users first.');
  }

  const tools = [
    {
      name: 'ChatGPT',
      description: 'Advanced AI chatbot for conversations and text generation',
      longDescription: 'ChatGPT is a state-of-the-art language model that can engage in natural conversations, answer questions, help with writing, coding, and much more.',
      website: 'https://chat.openai.com',
      category: 'text-generation',
      tags: ['chatbot', 'conversation', 'writing', 'ai-assistant'],
      pricing: 'freemium',
      pricingDetails: 'Free tier available, Plus subscription for $20/month',
      features: [
        'Natural language conversations',
        'Code generation and debugging',
        'Creative writing assistance',
        'Question answering',
        'Multiple language support'
      ],
      submittedBy: adminUser._id.toString(),
      status: 'approved',
      publishedAt: new Date(),
      views: 1250,
      likes: 89
    },
    {
      name: 'Midjourney',
      description: 'AI-powered image generation from text descriptions',
      longDescription: 'Midjourney is an independent research lab exploring new mediums of thought and expanding the imaginative powers of the human species.',
      website: 'https://midjourney.com',
      category: 'image-generation',
      tags: ['image-generation', 'art', 'creative', 'discord'],
      pricing: 'paid',
      pricingDetails: 'Subscription plans starting from $10/month',
      features: [
        'High-quality image generation',
        'Multiple art styles',
        'Discord integration',
        'Commercial usage rights',
        'Community gallery'
      ],
      submittedBy: adminUser._id.toString(),
      status: 'approved',
      publishedAt: new Date(),
      views: 980,
      likes: 67
    },
    {
      name: 'GitHub Copilot',
      description: 'AI pair programmer that helps you write code faster',
      longDescription: 'GitHub Copilot uses OpenAI Codex to suggest code and entire functions in real-time, right from your editor.',
      website: 'https://github.com/features/copilot',
      category: 'code-generation',
      tags: ['coding', 'programming', 'autocomplete', 'github'],
      pricing: 'paid',
      pricingDetails: '$10/month for individuals, $19/month for business',
      features: [
        'Code suggestions',
        'Multiple language support',
        'IDE integration',
        'Context-aware completions',
        'Code explanation'
      ],
      submittedBy: adminUser._id.toString(),
      status: 'approved',
      publishedAt: new Date(),
      views: 756,
      likes: 45
    }
  ];

  try {
    // Clear existing tools
    await Tool.deleteMany({});
    
    // Insert new tools
    const insertedTools = await Tool.insertMany(tools);
    console.log(`Seeded ${insertedTools.length} tools`);
    
    return insertedTools;
  } catch (error) {
    console.error('Error seeding tools:', error);
    throw error;
  }
};

export const seedAll = async () => {
  try {
    console.log('Starting database seeding...');
    
    await seedCategories();
    await seedUsers();
    await seedTools();
    
    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error during database seeding:', error);
    throw error;
  }
};
