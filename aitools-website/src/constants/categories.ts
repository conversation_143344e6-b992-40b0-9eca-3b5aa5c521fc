// AI工具分类的统一配置文件
// 这个文件包含所有分类的完整信息，确保整个应用中的一致性

export interface CategoryConfig {
  slug: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

export interface CategoryOption {
  value: string;
  label: string;
}

// 完整的分类配置
export const CATEGORY_CONFIGS: CategoryConfig[] = [
  {
    slug: 'text-generation',
    name: '文本生成',
    description: '利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等',
    icon: '📝',
    color: '#3B82F6'
  },
  {
    slug: 'image-generation',
    name: '图像生成',
    description: '使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等',
    icon: '🎨',
    color: '#10B981'
  },
  {
    slug: 'code-generation',
    name: '代码生成',
    description: '智能代码生成和编程辅助工具，提高开发效率',
    icon: '💻',
    color: '#8B5CF6'
  },
  {
    slug: 'data-analysis',
    name: '数据分析',
    description: '数据分析和可视化工具，帮助洞察数据价值',
    icon: '📊',
    color: '#F59E0B'
  },
  {
    slug: 'audio-processing',
    name: '音频处理',
    description: '音频处理、语音合成、音乐生成等音频AI工具',
    icon: '🎵',
    color: '#EF4444'
  },
  {
    slug: 'video-editing',
    name: '视频编辑',
    description: '视频生成、编辑、剪辑等视频处理AI工具',
    icon: '🎬',
    color: '#06B6D4'
  },
  {
    slug: 'translation',
    name: '语言翻译',
    description: '多语言翻译和本地化AI工具',
    icon: '🌐',
    color: '#84CC16'
  },
  {
    slug: 'search-engines',
    name: '搜索引擎',
    description: '智能搜索和信息检索AI工具',
    icon: '🔍',
    color: '#F97316'
  },
  {
    slug: 'education',
    name: '教育学习',
    description: '教育培训和学习辅助AI工具',
    icon: '📚',
    color: '#A855F7'
  },
  {
    slug: 'marketing',
    name: '营销工具',
    description: '数字营销和推广AI工具',
    icon: '📈',
    color: '#EC4899'
  },
  {
    slug: 'productivity',
    name: '生产力工具',
    description: '提高工作效率的AI工具',
    icon: '⚡',
    color: '#14B8A6'
  },
  {
    slug: 'customer-service',
    name: '客户服务',
    description: '客户支持和服务AI工具',
    icon: '🎧',
    color: '#F59E0B'
  }
];

// 生成分类选项（用于下拉框等）
export const CATEGORY_OPTIONS: CategoryOption[] = CATEGORY_CONFIGS.map(config => ({
  value: config.slug,
  label: config.name
}));

// 包含"所有分类"选项的分类选项
export const CATEGORY_OPTIONS_WITH_ALL: CategoryOption[] = [
  { value: '', label: '所有分类' },
  ...CATEGORY_OPTIONS
];

// 分类标签映射（slug -> 名称）
export const CATEGORY_LABELS: Record<string, string> = CATEGORY_CONFIGS.reduce(
  (acc, config) => {
    acc[config.slug] = config.name;
    return acc;
  },
  {} as Record<string, string>
);

// 分类元数据映射（slug -> 完整配置）
export const CATEGORY_METADATA: Record<string, CategoryConfig> = CATEGORY_CONFIGS.reduce(
  (acc, config) => {
    acc[config.slug] = config;
    return acc;
  },
  {} as Record<string, CategoryConfig>
);

// 获取分类配置的辅助函数
export const getCategoryConfig = (slug: string): CategoryConfig | undefined => {
  return CATEGORY_METADATA[slug];
};

// 获取分类名称的辅助函数
export const getCategoryName = (slug: string): string => {
  return CATEGORY_LABELS[slug] || slug;
};

// 验证分类是否存在的辅助函数
export const isValidCategory = (slug: string): boolean => {
  return slug in CATEGORY_METADATA;
};

// 获取所有分类slug的数组
export const CATEGORY_SLUGS = CATEGORY_CONFIGS.map(config => config.slug);
