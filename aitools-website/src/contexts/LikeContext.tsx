'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useSession } from 'next-auth/react';

// 工具点赞状态接口
interface ToolLikeState {
  liked: boolean;
  likes: number;
  loading: boolean;
}

// Context状态接口
interface LikeContextState {
  // 工具点赞状态映射 toolId -> ToolLikeState
  toolStates: Record<string, ToolLikeState>;
  // 切换点赞状态
  toggleLike: (toolId: string, forceUnlike?: boolean) => Promise<boolean>;
  // 获取工具点赞状态
  getToolState: (toolId: string) => ToolLikeState;
  // 初始化工具状态
  initializeToolState: (toolId: string, initialLikes: number, initialLiked?: boolean) => void;
  // 刷新工具状态
  refreshToolState: (toolId: string) => Promise<void>;
}

// 默认工具状态
const defaultToolState: ToolLikeState = {
  liked: false,
  likes: 0,
  loading: false
};

// 创建Context
const LikeContext = createContext<LikeContextState | null>(null);

// Provider组件
export function LikeProvider({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession();
  const [toolStates, setToolStates] = useState<Record<string, ToolLikeState>>({});

  // 获取工具状态
  const getToolState = useCallback((toolId: string): ToolLikeState => {
    return toolStates[toolId] || defaultToolState;
  }, [toolStates]);

  // 初始化工具状态
  const initializeToolState = useCallback((toolId: string, initialLikes: number, initialLiked = false) => {
    setToolStates(prev => {
      // 如果已经存在状态，不覆盖（避免重复初始化）
      if (prev[toolId]) {
        return prev;
      }
      return {
        ...prev,
        [toolId]: {
          liked: initialLiked,
          likes: initialLikes,
          loading: false
        }
      };
    });
  }, []);

  // 刷新工具状态（从服务器获取最新状态）
  const refreshToolState = useCallback(async (toolId: string) => {
    if (!session) return;

    try {
      const response = await fetch(`/api/tools/${toolId}/like`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setToolStates(prev => ({
            ...prev,
            [toolId]: {
              liked: data.data.liked,
              likes: data.data.likes,
              loading: false
            }
          }));
        }
      }
    } catch (error) {
      console.error('Failed to refresh tool state:', error);
    }
  }, [session]);

  // 切换点赞状态
  const toggleLike = useCallback(async (toolId: string, forceUnlike = false): Promise<boolean> => {
    if (!session) {
      return false; // 需要登录
    }

    // 设置加载状态
    setToolStates(prev => ({
      ...prev,
      [toolId]: {
        ...prev[toolId] || defaultToolState,
        loading: true
      }
    }));

    try {
      const requestBody = forceUnlike ? { forceUnlike: true } : {};
      
      const response = await fetch(`/api/tools/${toolId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // 更新状态
          setToolStates(prev => ({
            ...prev,
            [toolId]: {
              liked: data.data.liked,
              likes: data.data.likes,
              loading: false
            }
          }));
          return true; // 操作成功
        }
      }
      
      // 操作失败，恢复加载状态
      setToolStates(prev => ({
        ...prev,
        [toolId]: {
          ...prev[toolId] || defaultToolState,
          loading: false
        }
      }));
      return false;
    } catch (error) {
      console.error('Like request failed:', error);
      // 操作失败，恢复加载状态
      setToolStates(prev => ({
        ...prev,
        [toolId]: {
          ...prev[toolId] || defaultToolState,
          loading: false
        }
      }));
      return false;
    }
  }, [session]);

  // 当用户登录状态改变时，刷新所有已初始化的工具状态
  useEffect(() => {
    if (session) {
      // 用户登录后，刷新所有工具状态
      const toolIds = Object.keys(toolStates);
      toolIds.forEach(toolId => {
        refreshToolState(toolId);
      });
    } else {
      // 用户登出后，重置所有点赞状态为false
      setToolStates(prev => {
        const newStates: Record<string, ToolLikeState> = {};
        Object.keys(prev).forEach(toolId => {
          newStates[toolId] = {
            ...prev[toolId],
            liked: false,
            loading: false
          };
        });
        return newStates;
      });
    }
  }, [session]); // 移除refreshToolState依赖，避免无限循环

  const contextValue: LikeContextState = {
    toolStates,
    toggleLike,
    getToolState,
    initializeToolState,
    refreshToolState
  };

  return (
    <LikeContext.Provider value={contextValue}>
      {children}
    </LikeContext.Provider>
  );
}

// Hook for using the context
export function useLike() {
  const context = useContext(LikeContext);
  if (!context) {
    throw new Error('useLike must be used within a LikeProvider');
  }
  return context;
}
