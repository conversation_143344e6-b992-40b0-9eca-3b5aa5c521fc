'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import LikeButton from '@/components/tools/LikeButton';
import CommentSection from '@/components/tools/CommentSection';
import LoginModal from '@/components/auth/LoginModal';
import { apiClient, Tool } from '@/lib/api';
import { getToolPricingColor, getToolPricingText } from '@/constants/pricing';
import {
  ExternalLink,
  Heart,
  Eye,
  Tag,
  DollarSign,
  Share2
} from 'lucide-react';

interface ToolDetailClientProps {
  initialTool: Tool;
  toolId: string;
}

export default function ToolDetailClient({ initialTool, toolId }: ToolDetailClientProps) {
  const [tool, setTool] = useState<Tool>(initialTool);
  const [relatedTools, setRelatedTools] = useState<Tool[]>([]);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  useEffect(() => {
    fetchRelatedTools(tool.category);
  }, [tool.category]);

  const fetchRelatedTools = async (category: string) => {
    try {
      const response = await apiClient.getTools({
        category,
        status: 'published',
        limit: 3
      });

      if (response.success && response.data) {
        // 排除当前工具
        const filtered = response.data.tools.filter(t => t._id !== toolId);
        setRelatedTools(filtered.slice(0, 3));
      }
    } catch (err) {
      // 静默失败，相关工具不是必需的
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {/* Tool Header */}
          <article className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <header className="flex items-start justify-between mb-6">
              <div className="flex items-center space-x-4">
                {tool.logo ? (
                  <img
                    src={tool.logo}
                    alt={`${tool.name} logo`}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                ) : (
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-2xl">
                      {tool.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {tool.name}
                  </h1>
                  {tool.tagline && (
                    <p className="text-lg text-gray-600 mb-3">
                      {tool.tagline}
                    </p>
                  )}
                  <div className="flex items-center space-x-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getToolPricingColor(tool.pricing)}`}>
                      <DollarSign className="mr-1 h-4 w-4" />
                      {getToolPricingText(tool.pricing)}
                    </span>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>{tool.views || 0} 浏览</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Heart className="h-4 w-4" />
                        <span>{tool.likes || 0} 喜欢</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <LikeButton
                  toolId={tool._id}
                  initialLikes={tool.likes}
                  onLoginRequired={() => setIsLoginModalOpen(true)}
                />
                <button className="p-2 text-gray-400 hover:text-blue-500 transition-colors">
                  <Share2 className="h-5 w-5" />
                </button>
              </div>
            </header>

            {/* Description */}
            <div className="mb-6">
              <p className="text-gray-600 text-lg leading-relaxed">
                {tool.description}
              </p>
            </div>

            {/* Tags */}
            {tool.tags && tool.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-6">
                {tool.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer"
                  >
                    <Tag className="mr-1 h-3 w-3" />
                    {tag}
                  </span>
                ))}
              </div>
            )}

            {/* CTA Button */}
            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href={tool.website}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                <ExternalLink className="mr-2 h-5 w-5" />
                访问 {tool.name}
              </a>
            </div>
          </article>
        </div>

        {/* Sidebar */}
        <aside className="lg:col-span-1">
          {/* Tool Info */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">工具信息</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">分类</span>
                <span className="text-gray-900 font-medium">{tool.category}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">价格模式</span>
                <span className={`px-2 py-1 rounded text-sm font-medium ${getToolPricingColor(tool.pricing)}`}>
                  {getToolPricingText(tool.pricing)}
                </span>
              </div>
              {tool.launchDate && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">发布日期</span>
                  <span className="text-gray-900 font-medium">
                    {new Date(tool.launchDate).toLocaleDateString('zh-CN')}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Related Tools */}
          {relatedTools.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">相关工具</h3>
              <div className="space-y-4">
                {relatedTools.map((relatedTool) => (
                  <div key={relatedTool._id}>
                    <Link
                      href={`/tools/${relatedTool._id}`}
                      className="block p-3 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        {relatedTool.logo ? (
                          <img
                            src={relatedTool.logo}
                            alt={relatedTool.name}
                            className="w-10 h-10 rounded object-cover"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center">
                            <span className="text-white font-bold text-sm">
                              {relatedTool.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {relatedTool.name}
                          </h4>
                          <div className="flex items-center justify-between text-xs text-gray-500 mt-1">
                            <span className={`px-2 py-1 rounded ${getToolPricingColor(relatedTool.pricing)}`}>
                              {getToolPricingText(relatedTool.pricing)}
                            </span>
                            <div className="flex items-center space-x-2">
                              <span>{relatedTool.views || 0} 浏览</span>
                              <span>{relatedTool.likes || 0} 喜欢</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          )}
        </aside>
      </div>

      {/* Comments Section */}
      <div className="mt-12">
        <CommentSection
          toolId={tool._id}
          onLoginRequired={() => setIsLoginModalOpen(true)}
        />
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </>
  );
}
