import React from 'react';
import Link from 'next/link';
import PerformanceMonitor from '@/components/seo/PerformanceMonitor';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <PerformanceMonitor />

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">AI</span>
                </div>
                <span className="text-xl font-bold text-gray-900">AI Tools</span>
              </div>
              <p className="text-gray-600 mb-4">
                发现最新最好的 AI 工具，提升您的工作效率和创造力。
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                快速链接
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/tools" className="text-gray-600 hover:text-blue-600">
                    工具目录
                  </Link>
                </li>
                <li>
                  <Link href="/categories" className="text-gray-600 hover:text-blue-600">
                    分类浏览
                  </Link>
                </li>
                <li>
                  <Link href="/submit" className="text-gray-600 hover:text-blue-600">
                    提交工具
                  </Link>
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                支持
              </h3>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-gray-600 hover:text-blue-600">
                    帮助中心
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-blue-600">
                    联系我们
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-blue-600">
                    隐私政策
                  </a>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-200 mt-8 pt-8">
            <p className="text-center text-gray-600">
              © 2024 AI Tools Directory. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
