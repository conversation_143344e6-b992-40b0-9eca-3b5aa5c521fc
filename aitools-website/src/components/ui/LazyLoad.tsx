'use client';

import React, { useState, useEffect, useRef, ReactNode } from 'react';

interface LazyLoadProps {
  children: ReactNode;
  height?: number | string;
  offset?: number;
  placeholder?: ReactNode;
  className?: string;
  once?: boolean;
  onLoad?: () => void;
}

export default function LazyLoad({
  children,
  height = 200,
  offset = 100,
  placeholder,
  className = '',
  once = true,
  onLoad,
}: LazyLoadProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 如果已经加载过且设置了once，直接显示
    if (hasLoaded && once) {
      setIsVisible(true);
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsVisible(true);
          setHasLoaded(true);
          onLoad?.();
          
          // 如果设置了once，停止观察
          if (once) {
            observer.unobserve(element);
          }
        } else if (!once) {
          setIsVisible(false);
        }
      },
      {
        rootMargin: `${offset}px`,
        threshold: 0.1,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [offset, once, hasLoaded, onLoad]);

  const defaultPlaceholder = (
    <div 
      className="bg-gray-200 animate-pulse rounded"
      style={{ height: typeof height === 'number' ? `${height}px` : height }}
    />
  );

  return (
    <div 
      ref={elementRef}
      className={className}
      style={{ minHeight: typeof height === 'number' ? `${height}px` : height }}
    >
      {isVisible ? children : (placeholder || defaultPlaceholder)}
    </div>
  );
}

// 预设的懒加载配置
export const LazyLoadPresets = {
  image: {
    height: 200,
    offset: 50,
    placeholder: <div className="bg-gray-200 animate-pulse rounded aspect-video" />,
  },
  card: {
    height: 300,
    offset: 100,
    placeholder: (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            <div className="h-3 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    ),
  },
  section: {
    height: 400,
    offset: 200,
    placeholder: (
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="text-center mb-12">
              <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-gray-200 rounded-lg h-48"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    ),
  },
} as const;

// 懒加载图片组件
interface LazyImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  placeholder?: ReactNode;
}

export function LazyImage({
  src,
  alt,
  width,
  height,
  className = '',
  placeholder,
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setHasError(true);
    setIsLoaded(true);
  };

  const defaultPlaceholder = (
    <div 
      className="bg-gray-200 animate-pulse"
      style={{ width, height }}
    />
  );

  return (
    <LazyLoad
      height={height}
      placeholder={placeholder || defaultPlaceholder}
      className={className}
    >
      <img
        src={hasError ? '/images/placeholder.svg' : src}
        alt={alt}
        width={width}
        height={height}
        className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
        onLoad={handleLoad}
        onError={handleError}
        loading="lazy"
      />
    </LazyLoad>
  );
}

// 懒加载组件的Hook
export function useLazyLoad(offset = 100) {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        setIsVisible(entry.isIntersecting);
      },
      {
        rootMargin: `${offset}px`,
        threshold: 0.1,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [offset]);

  return { isVisible, elementRef };
}
