'use client';

import { useEffect } from 'react';

interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
}

export default function PerformanceMonitor() {
  useEffect(() => {
    // 只在生产环境中启用性能监控
    if (process.env.NODE_ENV !== 'production') {
      return;
    }

    const metrics: PerformanceMetrics = {};

    // 监控 First Contentful Paint (FCP)
    const observeFCP = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
          metrics.fcp = fcpEntry.startTime;
          reportMetric('FCP', fcpEntry.startTime);
        }
      });
      observer.observe({ entryTypes: ['paint'] });
    };

    // 监控 Largest Contentful Paint (LCP)
    const observeLCP = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        metrics.lcp = lastEntry.startTime;
        reportMetric('LCP', lastEntry.startTime);
      });
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    };

    // 监控 First Input Delay (FID)
    const observeFID = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          metrics.fid = entry.processingStart - entry.startTime;
          reportMetric('FID', entry.processingStart - entry.startTime);
        });
      });
      observer.observe({ entryTypes: ['first-input'] });
    };

    // 监控 Cumulative Layout Shift (CLS)
    const observeCLS = () => {
      let clsValue = 0;
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        metrics.cls = clsValue;
        reportMetric('CLS', clsValue);
      });
      observer.observe({ entryTypes: ['layout-shift'] });
    };

    // 监控 Time to First Byte (TTFB)
    const observeTTFB = () => {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
        metrics.ttfb = ttfb;
        reportMetric('TTFB', ttfb);
      }
    };

    // 报告性能指标
    const reportMetric = (name: string, value: number) => {
      // 在开发环境中输出到控制台
      if (process.env.NODE_ENV === 'development') {
        console.log(`Performance Metric - ${name}:`, value);
      }

      // 在生产环境中可以发送到分析服务
      // 例如 Google Analytics, Vercel Analytics 等
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'web_vitals', {
          event_category: 'Performance',
          event_label: name,
          value: Math.round(value),
          non_interaction: true,
        });
      }
    };

    // 检查浏览器支持
    if (typeof PerformanceObserver !== 'undefined') {
      observeFCP();
      observeLCP();
      observeFID();
      observeCLS();
    }

    observeTTFB();

    // 页面卸载时报告最终指标
    const reportFinalMetrics = () => {
      if (Object.keys(metrics).length > 0) {
        // 可以发送到分析服务
        console.log('Final Performance Metrics:', metrics);
      }
    };

    window.addEventListener('beforeunload', reportFinalMetrics);

    return () => {
      window.removeEventListener('beforeunload', reportFinalMetrics);
    };
  }, []);

  return null; // 这是一个无UI的监控组件
}

// 性能优化建议
export const PerformanceOptimizations = {
  // FCP 优化建议
  fcp: {
    good: 1800, // < 1.8s
    needsImprovement: 3000, // 1.8s - 3s
    suggestions: [
      '减少服务器响应时间',
      '消除阻塞渲染的资源',
      '压缩CSS和JavaScript',
      '使用CDN加速资源加载',
    ],
  },
  
  // LCP 优化建议
  lcp: {
    good: 2500, // < 2.5s
    needsImprovement: 4000, // 2.5s - 4s
    suggestions: [
      '优化图片加载',
      '预加载关键资源',
      '减少JavaScript执行时间',
      '使用服务端渲染',
    ],
  },
  
  // FID 优化建议
  fid: {
    good: 100, // < 100ms
    needsImprovement: 300, // 100ms - 300ms
    suggestions: [
      '减少JavaScript执行时间',
      '分割长任务',
      '使用Web Workers',
      '延迟加载非关键JavaScript',
    ],
  },
  
  // CLS 优化建议
  cls: {
    good: 0.1, // < 0.1
    needsImprovement: 0.25, // 0.1 - 0.25
    suggestions: [
      '为图片和视频设置尺寸属性',
      '避免在现有内容上方插入内容',
      '使用transform动画而非改变布局的动画',
      '预留广告位空间',
    ],
  },
};

// 声明全局gtag类型
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}
