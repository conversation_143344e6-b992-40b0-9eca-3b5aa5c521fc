'use client';

import React from 'react';
import CategoryCard from '@/components/CategoryCard';
import ErrorMessage from '@/components/ErrorMessage';
import { Grid, TrendingUp } from 'lucide-react';

// 分类页面组件

interface Category {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  toolCount: number;
}

interface CategoriesPageClientProps {
  categories: Category[];
  error: string | null;
}

export default function CategoriesPageClient({ categories, error }: CategoriesPageClientProps) {
  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ErrorMessage message={error} />
      </div>
    );
  }

  const popularCategories = categories
    .sort((a, b) => b.toolCount - a.toolCount)
    .slice(0, 6);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          <Grid className="inline-block mr-3 h-10 w-10 text-blue-600" />
          AI 工具分类
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          按功能分类浏览我们精选的 AI 工具集合。每个分类都包含经过验证的高质量工具，帮助您快速找到所需的解决方案。
        </p>
      </div>

      {/* Stats */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-100 rounded-lg p-8 mb-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
          <div>
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {categories.length}
            </div>
            <div className="text-gray-700">个分类</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {categories.reduce((sum, cat) => sum + cat.toolCount, 0)}
            </div>
            <div className="text-gray-700">个工具</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {categories.length > 0 ? Math.round(categories.reduce((sum, cat) => sum + cat.toolCount, 0) / categories.length) : 0}
            </div>
            <div className="text-gray-700">平均每分类工具数</div>
          </div>
        </div>
      </div>

      {/* Popular Categories */}
      <section className="mb-16">
        <div className="flex items-center mb-8">
          <TrendingUp className="h-8 w-8 text-blue-600 mr-3" />
          <h2 className="text-3xl font-bold text-gray-900">热门分类</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {popularCategories.map((category) => (
            <CategoryCard key={category._id} category={category} />
          ))}
        </div>
      </section>

      {/* All Categories */}
      <section>
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold text-gray-900">所有分类</h2>
          <div className="text-sm text-gray-600">
            {categories.length} 个分类
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {categories.map((category) => (
            <CategoryCard key={category._id} category={category} />
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="mt-16 bg-blue-600 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold text-white mb-4">
          没有找到您需要的分类？
        </h2>
        <p className="text-blue-100 mb-6">
          我们持续添加新的工具和分类。如果您有建议或想要提交新工具，请联系我们。
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="/submit"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-gray-50 transition-colors"
          >
            提交新工具
          </a>
          <a
            href="/contact"
            className="inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-lg text-white hover:bg-blue-700 transition-colors"
          >
            联系我们
          </a>
        </div>
      </section>
    </div>
  );
}
