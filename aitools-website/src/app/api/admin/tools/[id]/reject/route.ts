import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import mongoose from 'mongoose';

// POST /api/admin/tools/[id]/reject - 拒绝工具
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    const { id } = await params;
    const body = await request.json();

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的工具ID' },
        { status: 400 }
      );
    }

    // 验证拒绝原因
    if (!body.rejectReason || !body.rejectReason.trim()) {
      return NextResponse.json(
        { success: false, error: '拒绝原因是必需的' },
        { status: 400 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, error: '工具不存在' },
        { status: 404 }
      );
    }

    // 检查工具状态
    if (tool.status !== 'pending') {
      return NextResponse.json(
        { success: false, error: '只能拒绝待审核状态的工具' },
        { status: 400 }
      );
    }

    // 更新工具状态
    const updatedTool = await Tool.findByIdAndUpdate(
      id,
      {
        $set: {
          status: 'rejected',
          reviewedAt: new Date(),
          reviewedBy: body.reviewedBy || 'admin', // 后续应该使用实际的管理员ID
          reviewNotes: body.rejectReason,
          isActive: false
        },
        $unset: {
          // 清除之前的批准信息
          launchDate: 1
        }
      },
      { new: true, runValidators: true }
    );

    return NextResponse.json({
      success: true,
      data: updatedTool,
      message: '工具已被拒绝'
    });

  } catch (error) {
    console.error('Error rejecting tool:', error);
    return NextResponse.json(
      { success: false, error: '拒绝工具失败' },
      { status: 500 }
    );
  }
}
