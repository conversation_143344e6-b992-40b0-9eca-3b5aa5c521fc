import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import mongoose from 'mongoose';

// POST /api/admin/tools/[id]/approve - 批准工具
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    const { id } = await params;
    const body = await request.json();

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的工具ID' },
        { status: 400 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, error: '工具不存在' },
        { status: 404 }
      );
    }

    // 检查工具状态
    if (tool.status !== 'pending') {
      return NextResponse.json(
        { success: false, error: '只能批准待审核状态的工具' },
        { status: 400 }
      );
    }

    // 更新工具状态为approved，等待发布日期到达
    const updatedTool = await Tool.findByIdAndUpdate(
      id,
      {
        $set: {
          status: 'approved',
          reviewedAt: new Date(),
          reviewedBy: body.reviewedBy || 'admin', // 后续应该使用实际的管理员ID
          reviewNotes: body.reviewNotes || '',
          isActive: true
        },
        $unset: {
          // 清除之前的拒绝信息
          rejectedAt: 1,
          rejectReason: 1
        }
      },
      { new: true, runValidators: true }
    );

    return NextResponse.json({
      success: true,
      data: updatedTool,
      message: '工具已成功批准'
    });

  } catch (error) {
    console.error('Error approving tool:', error);
    return NextResponse.json(
      { success: false, error: '批准工具失败' },
      { status: 500 }
    );
  }
}
