import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';

// GET /api/admin/tools - 获取管理员工具列表（包括所有状态）
export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const sort = searchParams.get('sort') || 'submittedAt';
    const order = searchParams.get('order') || 'desc';

    // 构建查询条件
    const query: any = {};
    
    if (status && status !== 'all') {
      if (status === 'pending') {
        // 待审核状态：只显示免费工具或已付费的工具
        query.$and = [
          { status: 'pending' },
          {
            $or: [
              { launchOption: 'free' },
              {
                launchOption: 'paid',
                paymentStatus: 'completed'
              }
            ]
          }
        ];
      } else {
        query.status = status;
      }
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { submittedBy: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // 计算跳过的文档数
    const skip = (page - 1) * limit;

    // 构建排序条件
    const sortOrder = order === 'desc' ? -1 : 1;
    const sortQuery: any = {};
    sortQuery[sort] = sortOrder;

    // 执行查询（管理员可以看到所有字段）
    const tools = await Tool.find(query)
      .sort(sortQuery)
      .skip(skip)
      .limit(limit)
      .lean();

    // 获取总数
    const total = await Tool.countDocuments(query);

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // 获取状态统计
    const statusStats = await Tool.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const stats = {
      total,
      draft: statusStats.find(s => s._id === 'draft')?.count || 0,
      pending: statusStats.find(s => s._id === 'pending')?.count || 0,
      approved: statusStats.find(s => s._id === 'approved')?.count || 0,
      rejected: statusStats.find(s => s._id === 'rejected')?.count || 0
    };

    return NextResponse.json({
      success: true,
      data: {
        tools,
        stats,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: total,
          itemsPerPage: limit,
          hasNextPage,
          hasPrevPage
        }
      }
    });

  } catch (error) {
    console.error('Error fetching admin tools:', error);
    return NextResponse.json(
      { success: false, error: '获取工具列表失败' },
      { status: 500 }
    );
  }
}
