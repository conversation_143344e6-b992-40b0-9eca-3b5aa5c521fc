import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import dbConnect from '@/lib/mongodb';
import Order from '@/models/Order';
import Tool from '@/models/Tool';
import { constructWebhookEvent } from '@/lib/stripe';
import Stripe from 'stripe';

// POST /api/stripe/webhook - 处理Stripe Webhook事件
export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const headersList = await headers();
    const signature = headersList.get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      );
    }

    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error('STRIPE_WEBHOOK_SECRET is not configured');
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      );
    }

    // 验证Webhook签名
    let event: Stripe.Event;
    try {
      event = constructWebhookEvent(body, signature, webhookSecret);
    } catch (error) {
      console.error('Webhook signature verification failed:', error);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    await dbConnect();

    // 处理不同类型的事件
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object as Stripe.PaymentIntent);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

// 处理支付成功事件
async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  console.log('handlePaymentSucceeded called:...........', JSON.stringify(paymentIntent, null, 2))
  try {
    const orderId = paymentIntent.metadata.orderId;
    if (!orderId) {
      console.error('No orderId in payment intent metadata');
      return;
    }

    const order = await Order.findById(orderId);
    if (!order) {
      console.error(`Order not found: ${orderId}`);
      return;
    }

    // 更新订单状态
    await order.markAsPaid();
    order.stripePaymentIntentId = paymentIntent.id;
    order.paymentMethod = 'stripe';
    order.stripePaymentDetails = {
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: paymentIntent.status,
      created: new Date(paymentIntent.created * 1000)
    };
    await order.save();

    // 更新工具状态
    await Tool.findByIdAndUpdate(order.toolId, {
      $set: {
        paymentStatus: 'completed',
        paidAt: new Date(),
        status: 'pending' // 进入审核队列
      }
    });

    console.log(`Payment succeeded for order: ${orderId}`);

  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

// 处理支付失败事件
async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    const orderId = paymentIntent.metadata.orderId;
    if (!orderId) {
      console.error('No orderId in payment intent metadata');
      return;
    }

    const order = await Order.findById(orderId);
    if (!order) {
      console.error(`Order not found: ${orderId}`);
      return;
    }

    // 更新订单状态
    await order.markAsFailed();
    order.stripePaymentDetails = {
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: paymentIntent.status,
      created: new Date(paymentIntent.created * 1000),
      failureReason: paymentIntent.last_payment_error?.message
    };
    await order.save();

    console.log(`Payment failed for order: ${orderId}`);

  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

// 处理支付取消事件
async function handlePaymentCanceled(paymentIntent: Stripe.PaymentIntent) {
  try {
    const orderId = paymentIntent.metadata.orderId;
    if (!orderId) {
      console.error('No orderId in payment intent metadata');
      return;
    }

    const order = await Order.findById(orderId);
    if (!order) {
      console.error(`Order not found: ${orderId}`);
      return;
    }

    // 更新订单状态
    await order.cancel();
    order.stripePaymentDetails = {
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: paymentIntent.status,
      created: new Date(paymentIntent.created * 1000)
    };
    await order.save();

    console.log(`Payment canceled for order: ${orderId}`);

  } catch (error) {
    console.error('Error handling payment canceled:', error);
  }
}
