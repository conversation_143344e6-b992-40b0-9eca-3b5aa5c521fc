import React from 'react';
import { Metadata } from 'next';
import Layout from '@/components/Layout';
import { getBreadcrumbStructuredData } from '@/lib/seo/structuredData';
import { FileText, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

// 生成静态metadata
export const metadata: Metadata = {
  title: '服务条款 - AI工具导航',
  description: 'AI工具导航服务条款详细说明使用我们平台的规则、权利和义务。请仔细阅读并遵守相关条款。',
  keywords: '服务条款,使用协议,用户协议,法律条款,AI工具导航',
  authors: [{ name: 'AI工具导航团队' }],
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/terms`,
    siteName: 'AI工具导航',
    title: '服务条款 - AI工具导航',
    description: 'AI工具导航服务条款详细说明使用我们平台的规则、权利和义务。',
    images: [
      {
        url: '/og-terms.jpg',
        width: 1200,
        height: 630,
        alt: '服务条款 - AI工具导航',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: '服务条款 - AI工具导航',
    description: 'AI工具导航服务条款详细说明使用我们平台的规则、权利和义务。',
    images: ['/og-terms.jpg'],
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/terms`,
  },
};

export default function TermsPage() {
  // 生成结构化数据
  const breadcrumbStructuredData = getBreadcrumbStructuredData([
    { name: '首页', url: '/' },
    { name: '服务条款', url: '/terms' }
  ]);

  return (
    <Layout>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData)
        }}
      />

      {/* 面包屑导航 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav className="flex" aria-label="面包屑导航">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <a
                href="/"
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
              >
                首页
              </a>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">服务条款</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="bg-blue-100 rounded-full p-4">
              <FileText className="h-12 w-12 text-blue-600" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            服务条款
          </h1>
          <p className="text-xl text-gray-600">
            欢迎使用AI工具导航。请仔细阅读以下服务条款，使用我们的服务即表示您同意这些条款。
          </p>
          <p className="text-sm text-gray-500 mt-4">
            最后更新时间：2024年6月28日
          </p>
        </div>

        {/* Key Points */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="text-center p-6 bg-green-50 rounded-lg">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">您的权利</h3>
            <p className="text-sm text-gray-600">免费使用基础功能，享受优质服务</p>
          </div>
          <div className="text-center p-6 bg-yellow-50 rounded-lg">
            <AlertTriangle className="h-8 w-8 text-yellow-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">您的义务</h3>
            <p className="text-sm text-gray-600">遵守使用规则，尊重他人权益</p>
          </div>
          <div className="text-center p-6 bg-red-50 rounded-lg">
            <XCircle className="h-8 w-8 text-red-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">禁止行为</h3>
            <p className="text-sm text-gray-600">不得进行违法或有害活动</p>
          </div>
        </div>

        {/* Terms Content */}
        <div className="prose prose-lg max-w-none">
          <h2>1. 服务说明</h2>
          <h3>1.1 服务内容</h3>
          <p>AI工具导航（以下简称"我们"或"本平台"）是一个专业的人工智能工具发现和推荐平台，为用户提供：</p>
          <ul>
            <li>AI工具的发现、浏览和搜索服务</li>
            <li>工具分类、评价和推荐功能</li>
            <li>用户账户管理和个性化服务</li>
            <li>工具提交和发布服务</li>
            <li>社区互动功能（点赞、评论等）</li>
          </ul>

          <h3>1.2 服务变更</h3>
          <p>我们保留随时修改、暂停或终止部分或全部服务的权利，恕不另行通知。</p>

          <h2>2. 用户账户</h2>
          <h3>2.1 账户注册</h3>
          <ul>
            <li>您必须提供真实、准确的注册信息</li>
            <li>您有责任维护账户信息的准确性</li>
            <li>您有责任保护账户密码的安全</li>
            <li>一个邮箱地址只能注册一个账户</li>
          </ul>

          <h3>2.2 账户责任</h3>
          <ul>
            <li>您对使用您账户进行的所有活动负责</li>
            <li>如发现账户被盗用，请立即联系我们</li>
            <li>我们不对因账户信息泄露造成的损失承担责任</li>
          </ul>

          <h2>3. 使用规则</h2>
          <h3>3.1 合法使用</h3>
          <p>您同意仅将我们的服务用于合法目的，并遵守所有适用的法律法规。</p>

          <h3>3.2 禁止行为</h3>
          <p>在使用我们的服务时，您不得：</p>
          <ul>
            <li>发布虚假、误导性或欺诈性信息</li>
            <li>侵犯他人的知识产权或其他权利</li>
            <li>发布违法、有害、威胁、辱骂或诽谤性内容</li>
            <li>进行垃圾邮件发送或其他形式的滥用</li>
            <li>尝试破坏或干扰我们的服务</li>
            <li>使用自动化工具恶意抓取数据</li>
            <li>创建虚假账户或进行虚假评价</li>
          </ul>

          <h2>4. 内容政策</h2>
          <h3>4.1 用户生成内容</h3>
          <ul>
            <li>您对提交的所有内容（工具信息、评论等）负责</li>
            <li>您保证拥有提交内容的合法权利</li>
            <li>您授予我们使用、展示和分发您内容的权利</li>
          </ul>

          <h3>4.2 内容审核</h3>
          <ul>
            <li>我们保留审核、编辑或删除任何内容的权利</li>
            <li>我们可能使用自动化工具进行内容筛选</li>
            <li>违规内容将被删除，严重违规可能导致账户封禁</li>
          </ul>

          <h2>5. 知识产权</h2>
          <h3>5.1 平台知识产权</h3>
          <ul>
            <li>本平台的设计、代码、商标等均受知识产权保护</li>
            <li>未经许可，不得复制、修改或分发我们的内容</li>
            <li>我们尊重他人的知识产权，也期望用户同样如此</li>
          </ul>

          <h3>5.2 用户内容权利</h3>
          <ul>
            <li>您保留对自己创作内容的所有权</li>
            <li>您授予我们在平台上使用您内容的非独占许可</li>
            <li>该许可在您删除内容或关闭账户后终止</li>
          </ul>

          <h2>6. 付费服务</h2>
          <h3>6.1 付费功能</h3>
          <ul>
            <li>某些高级功能可能需要付费使用</li>
            <li>付费服务的具体条款将在购买时说明</li>
            <li>付费后的服务变更将提前通知用户</li>
          </ul>

          <h3>6.2 退款政策</h3>
          <ul>
            <li>除法律要求外，付费服务一般不支持退款</li>
            <li>如因我们的原因导致服务无法提供，将按比例退款</li>
            <li>具体退款条件请参考购买时的说明</li>
          </ul>

          <h2>7. 免责声明</h2>
          <h3>7.1 服务免责</h3>
          <ul>
            <li>我们的服务按"现状"提供，不提供任何明示或暗示的保证</li>
            <li>我们不保证服务的连续性、准确性或完整性</li>
            <li>我们不对第三方工具的质量或安全性负责</li>
          </ul>

          <h3>7.2 损失免责</h3>
          <ul>
            <li>我们不对因使用服务而产生的任何直接或间接损失负责</li>
            <li>包括但不限于数据丢失、业务中断、利润损失等</li>
            <li>我们的责任限额不超过您支付的服务费用</li>
          </ul>

          <h2>8. 服务终止</h2>
          <h3>8.1 终止条件</h3>
          <p>在以下情况下，我们可能暂停或终止您的账户：</p>
          <ul>
            <li>违反本服务条款</li>
            <li>进行欺诈或非法活动</li>
            <li>长期不活跃的账户</li>
            <li>其他我们认为必要的情况</li>
          </ul>

          <h3>8.2 终止后果</h3>
          <ul>
            <li>账户终止后，您将无法访问相关服务</li>
            <li>您的数据可能被删除（法律要求保留的除外）</li>
            <li>未使用的付费服务余额可能无法退还</li>
          </ul>

          <h2>9. 法律适用</h2>
          <h3>9.1 管辖法律</h3>
          <p>本服务条款受中华人民共和国法律管辖。</p>

          <h3>9.2 争议解决</h3>
          <ul>
            <li>因本服务条款产生的争议，双方应首先协商解决</li>
            <li>协商不成的，可向我们所在地人民法院提起诉讼</li>
          </ul>

          <h2>10. 条款变更</h2>
          <h3>10.1 更新通知</h3>
          <p>我们可能会不时更新本服务条款。重大变更时，我们会：</p>
          <ul>
            <li>在网站上发布显著通知</li>
            <li>向您的注册邮箱发送通知</li>
            <li>在您下次登录时显示更新提醒</li>
          </ul>

          <h3>10.2 生效时间</h3>
          <p>更新后的条款将在发布后立即生效。继续使用我们的服务即表示您接受新的条款。</p>

          <h2>11. 联系方式</h2>
          <p>如果您对本服务条款有任何疑问，请通过以下方式联系我们：</p>
          <ul>
            <li>邮箱：<EMAIL></li>
            <li>联系表单：<a href="/contact">联系我们页面</a></li>
          </ul>
        </div>

        {/* Footer */}
        <div className="mt-12 p-6 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-600 text-center">
            本服务条款构成您与AI工具导航之间的完整协议。
            使用我们的服务即表示您已阅读、理解并同意遵守本条款。
          </p>
        </div>
      </div>
    </Layout>
  );
}
