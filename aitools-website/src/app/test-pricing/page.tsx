'use client';

import React from 'react';
import Layout from '@/components/Layout';
import { 
  PRICING_CONFIG, 
  LAUNCH_OPTIONS, 
  TOOL_PRICING_OPTIONS, 
  TOOL_PRICING_FORM_OPTIONS,
  formatPrice,
  formatStripeAmount,
  getToolPricingColor,
  getToolPricingText
} from '@/constants/pricing';

export default function TestPricingPage() {
  return (
    <Layout>
      <div className="max-w-4xl mx-auto py-8 px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">统一价格配置测试页面</h1>
        
        {/* 基础价格配置 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">基础价格配置 (PRICING_CONFIG)</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">优先发布服务</h3>
              <div className="space-y-1 text-sm">
                <p>显示价格: {formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice)}</p>
                <p>Stripe金额: {PRICING_CONFIG.PRIORITY_LAUNCH.stripeAmount} 分</p>
                <p>格式化Stripe金额: {formatStripeAmount(PRICING_CONFIG.PRIORITY_LAUNCH.stripeAmount)}</p>
                <p>产品名称: {PRICING_CONFIG.PRIORITY_LAUNCH.productName}</p>
                <p>描述: {PRICING_CONFIG.PRIORITY_LAUNCH.description}</p>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">免费发布服务</h3>
              <div className="space-y-1 text-sm">
                <p>显示价格: {formatPrice(PRICING_CONFIG.FREE_LAUNCH.displayPrice)}</p>
                <p>Stripe金额: {PRICING_CONFIG.FREE_LAUNCH.stripeAmount} 分</p>
                <p>格式化Stripe金额: {formatStripeAmount(PRICING_CONFIG.FREE_LAUNCH.stripeAmount)}</p>
                <p>产品名称: {PRICING_CONFIG.FREE_LAUNCH.productName}</p>
                <p>描述: {PRICING_CONFIG.FREE_LAUNCH.description}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 发布选项配置 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">发布选项配置 (LAUNCH_OPTIONS)</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {LAUNCH_OPTIONS.map((option) => (
              <div key={option.id} className={`p-4 rounded-lg border-2 ${option.recommended ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900">{option.title}</h3>
                  {option.recommended && (
                    <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded">推荐</span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-2">{option.description}</p>
                <p className="text-lg font-semibold text-blue-600 mb-2">{formatPrice(option.price)}</p>
                <ul className="text-xs text-gray-500 space-y-1">
                  {option.features.map((feature, index) => (
                    <li key={index}>• {feature}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* 工具定价选项 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">工具定价选项</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">筛选选项 (TOOL_PRICING_OPTIONS)</h3>
              <div className="space-y-2">
                {TOOL_PRICING_OPTIONS.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded text-xs ${getToolPricingColor(option.value)}`}>
                      {option.label}
                    </span>
                    <span className="text-sm text-gray-600">({option.value || '全部'})</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">表单选项 (TOOL_PRICING_FORM_OPTIONS)</h3>
              <div className="space-y-2">
                {TOOL_PRICING_FORM_OPTIONS.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded text-xs ${getToolPricingColor(option.value)}`}>
                      {getToolPricingText(option.value)}
                    </span>
                    <span className="text-sm text-gray-600">({option.value})</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 辅助函数测试 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold mb-4">辅助函数测试</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">价格格式化</h3>
              <div className="space-y-1 text-sm">
                <p>formatPrice(0): {formatPrice(0)}</p>
                <p>formatPrice(99): {formatPrice(99)}</p>
                <p>formatPrice(199): {formatPrice(199)}</p>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Stripe金额格式化</h3>
              <div className="space-y-1 text-sm">
                <p>formatStripeAmount(0): {formatStripeAmount(0)}</p>
                <p>formatStripeAmount(9900): {formatStripeAmount(9900)}</p>
                <p>formatStripeAmount(19900): {formatStripeAmount(19900)}</p>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">定价类型样式</h3>
              <div className="space-y-2">
                <span className={`px-2 py-1 rounded text-xs ${getToolPricingColor('free')}`}>
                  {getToolPricingText('free')}
                </span>
                <br />
                <span className={`px-2 py-1 rounded text-xs ${getToolPricingColor('freemium')}`}>
                  {getToolPricingText('freemium')}
                </span>
                <br />
                <span className={`px-2 py-1 rounded text-xs ${getToolPricingColor('paid')}`}>
                  {getToolPricingText('paid')}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
