import React from 'react';
import { Metadata } from 'next';
import Layout from '@/components/Layout';
import { getBreadcrumbStructuredData } from '@/lib/seo/structuredData';
import { Mail, MessageSquare, Clock, MapPin } from 'lucide-react';

// 生成静态metadata
export const metadata: Metadata = {
  title: '联系我们 - AI工具导航',
  description: '联系AI工具导航团队，获取技术支持、商务合作或提供反馈建议。我们期待与您的交流。',
  keywords: '联系我们,技术支持,商务合作,反馈建议,AI工具导航',
  authors: [{ name: 'AI工具导航团队' }],
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/contact`,
    siteName: 'AI工具导航',
    title: '联系我们 - AI工具导航',
    description: '联系AI工具导航团队，获取技术支持、商务合作或提供反馈建议。',
    images: [
      {
        url: '/og-contact.jpg',
        width: 1200,
        height: 630,
        alt: '联系我们 - AI工具导航',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: '联系我们 - AI工具导航',
    description: '联系AI工具导航团队，获取技术支持、商务合作或提供反馈建议。',
    images: ['/og-contact.jpg'],
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/contact`,
  },
};

export default function ContactPage() {
  // 生成结构化数据
  const breadcrumbStructuredData = getBreadcrumbStructuredData([
    { name: '首页', url: '/' },
    { name: '联系我们', url: '/contact' }
  ]);

  // 生成联系页面结构化数据
  const contactStructuredData = {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "联系我们 - AI工具导航",
    "description": "联系AI工具导航团队，获取技术支持、商务合作或提供反馈建议",
    "url": `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/contact`,
    "mainEntity": {
      "@type": "Organization",
      "name": "AI工具导航",
      "contactPoint": [
        {
          "@type": "ContactPoint",
          "contactType": "customer service",
          "email": "<EMAIL>",
          "availableLanguage": "Chinese"
        },
        {
          "@type": "ContactPoint",
          "contactType": "business",
          "email": "<EMAIL>",
          "availableLanguage": "Chinese"
        }
      ]
    }
  };

  return (
    <Layout>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(contactStructuredData)
        }}
      />

      {/* 面包屑导航 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav className="flex" aria-label="面包屑导航">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <a
                href="/"
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
              >
                首页
              </a>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">联系我们</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            联系我们
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            我们很乐意听到您的声音。无论是技术支持、商务合作还是反馈建议，请随时与我们联系。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">联系方式</h2>
            
            <div className="space-y-6">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <Mail className="h-6 w-6 text-blue-600 mt-1" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">邮箱联系</h3>
                  <p className="text-gray-600 mt-1">
                    一般咨询：<a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800"><EMAIL></a>
                  </p>
                  <p className="text-gray-600">
                    商务合作：<a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800"><EMAIL></a>
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <Clock className="h-6 w-6 text-blue-600 mt-1" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">响应时间</h3>
                  <p className="text-gray-600 mt-1">
                    我们通常在24小时内回复您的邮件
                  </p>
                  <p className="text-gray-600">
                    工作时间：周一至周五 9:00-18:00 (北京时间)
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <MessageSquare className="h-6 w-6 text-blue-600 mt-1" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">常见问题</h3>
                  <p className="text-gray-600 mt-1">
                    在联系我们之前，您可以查看我们的常见问题解答
                  </p>
                  <a href="/faq" className="text-blue-600 hover:text-blue-800">查看FAQ →</a>
                </div>
              </div>
            </div>

            {/* Contact Types */}
            <div className="mt-12">
              <h3 className="text-xl font-bold text-gray-900 mb-6">我们能为您提供什么帮助？</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">技术支持</h4>
                  <p className="text-sm text-gray-600">网站使用问题、账户问题、功能咨询</p>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">商务合作</h4>
                  <p className="text-sm text-gray-600">工具推广、广告投放、战略合作</p>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">内容建议</h4>
                  <p className="text-sm text-gray-600">工具推荐、分类建议、功能改进</p>
                </div>
                <div className="bg-orange-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">媒体合作</h4>
                  <p className="text-sm text-gray-600">采访邀请、新闻发布、行业报告</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-gray-50 rounded-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">发送消息</h2>
            <form className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  姓名 *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入您的姓名"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱 *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入您的邮箱地址"
                />
              </div>

              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                  主题 *
                </label>
                <select
                  id="subject"
                  name="subject"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">请选择咨询类型</option>
                  <option value="technical">技术支持</option>
                  <option value="business">商务合作</option>
                  <option value="content">内容建议</option>
                  <option value="media">媒体合作</option>
                  <option value="other">其他</option>
                </select>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  消息内容 *
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={6}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请详细描述您的问题或需求..."
                ></textarea>
              </div>

              <button
                type="submit"
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium"
              >
                发送消息
              </button>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  );
}
