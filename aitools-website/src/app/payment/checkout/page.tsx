'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import Layout from '@/components/Layout';
import StripeCheckoutForm from '@/components/StripeCheckoutForm';
import { formatStripeAmount } from '@/constants/pricing';
import { CreditCard, Shield, CheckCircle, AlertCircle } from 'lucide-react';

// 初始化Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export default function CheckoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, status } = useSession();
  const [order, setOrder] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [clientSecret, setClientSecret] = useState<string>('');

  const orderId = searchParams.get('orderId');

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }

    if (status === 'authenticated' && orderId) {
      fetchOrderInfo();
    }
  }, [status, orderId]);

  const fetchOrderInfo = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`);
      const data = await response.json();

      if (data.success) {
        setOrder(data.data);

        // 检查订单状态
        if (data.data.status === 'completed') {
          router.push(`/submit/success?toolId=${data.data.toolId}`);
          return;
        }

        if (data.data.status !== 'pending') {
          setError('订单状态异常');
          return;
        }

        // 创建支付意图
        await createPaymentIntent();
      } else {
        setError('订单不存在');
      }
    } catch (err) {
      setError('获取订单信息失败');
    } finally {
      setLoading(false);
    }
  };

  const createPaymentIntent = async () => {
    try {
      const response = await fetch('/api/stripe/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId }),
      });

      const data = await response.json();

      if (data.success) {
        setClientSecret(data.data.clientSecret);
      } else {
        setError(data.message || '创建支付失败');
      }
    } catch (err) {
      setError('创建支付失败');
    }
  };

  const handlePaymentSuccess = () => {
    router.push(`/submit/success?toolId=${order.toolId}&paid=true`);
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载订单信息...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">支付出错</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => router.push('/submit')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              返回提交页面
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  if (!order) {
    return null;
  }

  return (
    <Layout>
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <CreditCard className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            完成支付
          </h1>
          <p className="text-lg text-gray-600">
            为您的工具选择优先发布服务
          </p>
        </div>

        {/* Order Summary */}
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">订单详情</h2>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">服务类型</span>
              <span className="font-medium">工具优先发布</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">工具名称</span>
              <span className="font-medium">{order.tool?.name || '加载中...'}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">发布日期</span>
              <span className="font-medium">
                {new Date(order.selectedLaunchDate).toLocaleDateString('zh-CN')}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">订单号</span>
              <span className="font-medium text-sm">{order._id}</span>
            </div>
            
            <hr className="my-4" />
            
            <div className="flex justify-between text-lg font-semibold">
              <span>总计</span>
              <span className="text-blue-600">{formatStripeAmount(order.amount)}</span>
            </div>
          </div>
        </div>

        {/* Service Features */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            优先发布服务包含：
          </h3>
          <ul className="space-y-2">
            <li className="flex items-center text-blue-800">
              <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              可选择任意发布日期
            </li>
            <li className="flex items-center text-blue-800">
              <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              优先审核处理（1个工作日内）
            </li>
            <li className="flex items-center text-blue-800">
              <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              首页推荐位置展示
            </li>
            <li className="flex items-center text-blue-800">
              <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              专属客服支持
            </li>
          </ul>
        </div>

        {/* Security Notice */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
          <div className="flex items-center text-gray-700">
            <Shield className="h-5 w-5 mr-2 text-green-500" />
            <span className="text-sm">
              您的支付信息受到银行级别的安全保护
            </span>
          </div>
        </div>

        {/* Stripe Payment Form */}
        {clientSecret && (
          <Elements
            stripe={stripePromise}
            options={{
              clientSecret,
              appearance: {
                theme: 'stripe',
                variables: {
                  colorPrimary: '#2563eb',
                }
              }
            }}
          >
            <StripeCheckoutForm
              onSuccess={handlePaymentSuccess}
              amount={order.amount}
            />
          </Elements>
        )}

        {error && (
          <div className="text-center">
            <p className="text-red-600 text-sm mt-4">{error}</p>
          </div>
        )}

        <p className="text-gray-500 text-sm mt-4 text-center">
          点击支付即表示您同意我们的服务条款和隐私政策
        </p>
      </div>
    </Layout>
  );
}
