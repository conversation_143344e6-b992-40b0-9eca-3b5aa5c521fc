'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Layout from '@/components/Layout';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import SuccessMessage from '@/components/SuccessMessage';
import LoginModal from '@/components/auth/LoginModal';
import { TOOL_PRICING_FORM_OPTIONS } from '@/constants/pricing';
import { CATEGORY_OPTIONS } from '@/constants/categories';
import {
  Upload,
  Link as LinkIcon,
  Info
} from 'lucide-react';
import { MAX_TAGS_COUNT } from '@/constants/tags';
import TagSelector from '@/components/TagSelector';

// 使用统一的分类选项配置
const categories = CATEGORY_OPTIONS;

// 使用统一的价格选项配置
const pricingOptions = TOOL_PRICING_FORM_OPTIONS;

interface FormData {
  name: string;
  tagline: string;
  description: string;
  website: string;
  logo: string;
  category: string;
  tags: string[];
  pricing: string;
}

export default function SubmitPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    tagline: '',
    description: '',
    website: '',
    logo: '',
    category: '',
    tags: [],
    pricing: ''
  });





  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitMessage, setSubmitMessage] = useState('');
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string>('');

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = '工具名称是必填项';
    if (!formData.description.trim()) newErrors.description = '工具描述是必填项';
    if (!formData.website.trim()) newErrors.website = '官方网站是必填项';
    if (!formData.category) newErrors.category = '请选择一个分类';
    if (!formData.pricing) newErrors.pricing = '请选择价格模式';

    // URL validation
    if (formData.website && !formData.website.match(/^https?:\/\/.+/)) {
      newErrors.website = '请输入有效的网站地址（以 http:// 或 https:// 开头）';
    }



    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 检查用户是否已登录
    if (!session) {
      setIsLoginModalOpen(true);
      return;
    }

    if (!validateForm()) return;

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      let logoUrl = formData.logo;

      // 如果有选择的logo文件，先上传
      if (logoFile) {
        const logoFormData = new FormData();
        logoFormData.append('logo', logoFile);

        const uploadResponse = await fetch('/api/upload/logo', {
          method: 'POST',
          body: logoFormData,
        });

        const uploadData = await uploadResponse.json();
        if (uploadData.success) {
          logoUrl = uploadData.data.url;
        } else {
          throw new Error(uploadData.message || 'Logo上传失败');
        }
      }

      // 调用新的提交API
      const response = await fetch('/api/tools/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          tagline: formData.tagline,
          description: formData.description,
          website: formData.website,
          logo: logoUrl || undefined,
          category: formData.category,
          tags: formData.tags,
          pricing: formData.pricing
        }),
      });

      const data = await response.json();

      if (data.success) {
        // 跳转到发布日期选择页面
        router.push(`/submit/launch-date/${data.data.toolId}`);
      } else {
        setSubmitStatus('error');
        setSubmitMessage(data.message || '提交失败，请重试');
      }
    } catch (error) {
      console.error('Error submitting tool:', error);
      setSubmitStatus('error');
      setSubmitMessage('网络错误，请检查连接后重试');
    } finally {
      setIsSubmitting(false);
    }
  };



  // 处理logo文件选择
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setLogoFile(file);
      // 创建预览URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            <Upload className="inline-block mr-3 h-8 w-8 text-blue-600" />
            提交 AI 工具
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            分享您发现或开发的优秀 AI 工具，帮助更多人发现和使用这些工具。我们会在审核通过后发布您的提交。
          </p>
        </div>

        {/* Success/Error Messages */}
        {submitStatus === 'success' && (
          <SuccessMessage
            message={submitMessage || '工具提交成功！我们会在 1-3 个工作日内审核您的提交。'}
            onClose={() => setSubmitStatus('idle')}
            className="mb-6"
          />
        )}

        {submitStatus === 'error' && (
          <ErrorMessage
            message={submitMessage || '提交失败，请检查网络连接后重试。'}
            onClose={() => setSubmitStatus('idle')}
            className="mb-6"
          />
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {/* Basic Information */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">基本信息</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  工具名称 *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="例如：ChatGPT"
                />
                {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  工具标语（可选）
                </label>
                <input
                  type="text"
                  value={formData.tagline}
                  onChange={(e) => setFormData(prev => ({ ...prev, tagline: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="简短描述工具的核心价值"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  官方网站 *
                </label>
                <div className="relative">
                  <input
                    type="url"
                    value={formData.website}
                    onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                    className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.website ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="https://example.com"
                  />
                  <LinkIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                </div>
                {errors.website && <p className="text-red-600 text-sm mt-1">{errors.website}</p>}
              </div>
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                工具描述 *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={4}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.description ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="详细描述这个 AI 工具的功能和特点..."
              />
              {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Logo图片（可选）
              </label>
              <div className="flex items-center gap-4">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleLogoChange}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
                {logoPreview && (
                  <div className="flex-shrink-0">
                    <img
                      src={logoPreview}
                      alt="Logo预览"
                      className="w-16 h-16 object-cover rounded-lg border border-gray-300"
                    />
                  </div>
                )}
              </div>
              <p className="text-sm text-gray-500 mt-1">
                支持 JPEG、PNG、GIF、WebP 格式，文件大小不超过 5MB
              </p>
            </div>
          </div>

          {/* Category and Pricing */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">分类和定价</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  工具分类 *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.category ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">请选择分类</option>
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
                {errors.category && <p className="text-red-600 text-sm mt-1">{errors.category}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  价格模式 *
                </label>
                <select
                  value={formData.pricing}
                  onChange={(e) => setFormData(prev => ({ ...prev, pricing: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.pricing ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">请选择价格模式</option>
                  {pricingOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {errors.pricing && <p className="text-red-600 text-sm mt-1">{errors.pricing}</p>}
              </div>
            </div>
          </div>

          {/* Tags */}
          <div className="mb-8">
            <TagSelector
              selectedTags={formData.tags}
              onTagsChange={(tags) => setFormData(prev => ({ ...prev, tags }))}
              maxTags={MAX_TAGS_COUNT}
            />
          </div>



          {/* User Info Display */}
          {session && (
            <div className="mb-8 bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-green-800 mb-2">提交者信息</h3>
              <p className="text-sm text-green-700">
                提交者：{session.user?.name || session.user?.email}
              </p>
              <p className="text-sm text-green-700">
                邮箱：{session.user?.email}
              </p>
            </div>
          )}

          {/* Guidelines */}
          <div className="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-start">
              <Info className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-blue-900 mb-2">提交指南</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 请确保提交的是真实存在且可正常访问的 AI 工具</li>
                  <li>• 工具描述应该准确、客观，避免过度营销</li>
                  <li>• 我们会在 1-3 个工作日内审核您的提交</li>
                  <li>• 审核通过后，工具将出现在我们的目录中</li>
                  <li>• 如有问题，我们会通过邮箱联系您</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`px-8 py-3 rounded-lg font-medium transition-colors ${
                isSubmitting
                  ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <LoadingSpinner size="sm" className="mr-2" />
                  提交中...
                </div>
              ) : (
                '提交工具'
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </Layout>
  );
}